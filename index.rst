.. imotion_front_camera documentation master file
   You can adapt this file completely to your liking, but it should at least
   contain the root `toctree` directive.

Welcome to Imotion Front Camera documentation!
=================================================

本文档包含了 Imotion Front Camera 应用程序的完整技术文档，涵盖各个功能组件的设计和实现。

.. toctree::
   :maxdepth: 2
   :caption: 核心组件文档
   :numbered:

   cpj_chery/components/SIO_NET_CFG/icar05/doc/index


组件概览
==================

核心组件
--------

* **SIO_NET** - 网络通信组件
  
  - 负责处理车辆CAN网络通信
  - 提供消息收发、信号处理、E2E保护等功能
  - 支持多CAN通道和诊断功能


其他组件
--------

项目还包含以下组件模块：

.. hlist::
   :columns: 3

   * DMK_LCD - 显示控制
   * DMK_LDA - 车道检测辅助
   * DMK_PLA - 泊车辅助
   * DMK_TOS - 交通标识识别
   * HSM - 硬件安全模块
   * PCP_EVI - 环境感知
   * PCP_MSF - 多传感器融合
   * PCP_SEM - 语义地图
   * SAF_FCS - 功能安全
   * SDU_DAL - 数据抽象层
   * SMU_DIA - 诊断管理

构建和开发
==================

.. toctree::
   :maxdepth: 1
   :caption: 开发指南

快速开始
--------

本项目使用 CMake 构建系统，支持多种目标平台和配置。

可用的构建任务：

* **Build** - 构建应用程序
* **Rebuild** - 完全重建
* **SIL构建** - 软件在环测试构建
* **文档生成** - 生成API文档

版本信息
========

:项目: imotion_front_camera
:版本: 最新开发版
:构建系统: CMake + Make/Ninja
:文档系统: Sphinx + Doxygen + Breathe
:支持平台: 多车型平台

索引和搜索
==================

* :ref:`genindex`
* :ref:`search`
    