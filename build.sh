#!/bin/bash
set -e
# this file is only used under ubuntu platform
# rootPath=$(pwd)
# cd $rootPath
# python3 ./ifc_rte/tools/flight/scripts/build.py $*
# ./build.bat -m clean -t sil -p cpj_chery -d ifc3 -vh icar05 -c Release
echo "Building project with the following parameters:"
echo "Mode: full"
echo "Target: doc"
echo "Project: cpj_chery"
echo "Device: ifc3s"
echo "Vehicle: icar05"
echo "Configuration: Release"
python3 ./ifc_rte/tools/flight/scripts/build.py -m full -t docs -p cpj_chery -d ifc3s -vh icar05 -c Release