#=============================================================================
#  C O P Y R I G H T
#-----------------------------------------------------------------------------
#  Copyright (c) 2021 by iMotion AI. All rights reserved.
#=============================================================================
# Author(s): Jason Luo
#=============================================================================
    
#=============================================================================
# Add Subdirectories
#=============================================================================

add_subdirectory(components)
add_subdirectory(ifc_infrastructure)
add_subdirectory(ifc_interface)
add_subdirectory(ifc_rte)
add_subdirectory(${ZX_CUSTOMER_PROJECT})
# add_subdirectory(cmake_build/docs)
#=============================================================================
# Interface Declaration: prvif
#=============================================================================
add_library(prvif INTERFACE IMPORTED GLOBAL)
target_link_libraries(
    prvif
    INTERFACE
        prvif::pf
        prvif::cpj
)

#=============================================================================
# Executable Declaration
#=============================================================================
# add_executable(
#     ${ZX_PROJECT_TARGET_BIN_NAME}  
#     ${ZX_PROJECT_STARTUP_SOURCE_FILES})
    
#=============================================================================
# Link libraries Declaration
#=============================================================================

    include(cmake_build/project_build_target_boot.cmake)
    include(cmake_build/project_build_target_bsw.cmake)

#=============================================================================
# Documentation Generation
#=============================================================================

set(CMAKE_EXPORT_COMPILE_COMMANDS ON)
set(CMAKE_COLOR_DIAGNOSTICS ON)

# Documentation build options
option(BUILD_DOCS "Build documentation" ON)
option(BUILD_API_DOCS "Build API documentation" ON)
option(BUILD_USER_DOCS "Build user documentation" ON)
option(BUILD_DOC_PDF "Build PDF documentation" OFF)
option(BUILD_DOC_EPUB "Build EPUB documentation" OFF)
option(BUILD_DOC_LATEX "Build LaTeX documentation" OFF)

# find Doxygen and Python3
find_package(Doxygen OPTIONAL_COMPONENTS dot mscgen dia)
find_package(Python3 COMPONENTS Interpreter)

# get all modules in the project
get_property(ALL_MODULES GLOBAL PROPERTY PROJECT_ALL_MODULES)

# Collect documented modules and all source code directories
set(DOCUMENTED_MODULES "")
set(ALL_SOURCE_DIRS "")
set(ALL_INCLUDE_DIRS "")
# set(ALL_DOCUMENTATION_SOURCES "")
# set(ALL_DOCUMENTATION_HEADERS "")
set(ALL_EXECUTABLE_FILES "")
set(DOXYGEN_OUTPUT_PATH_XML_DICT_STRING "")
set(DOXYGEN_OUTPUT_PATH_HTML_DICT_STRING "")
foreach(MODULE IN LISTS ALL_MODULES)
    # Check if there is documentation
    get_target_property(HAS_DOCS ${MODULE} HAS_DOCUMENTATION)
    if(HAS_DOCS)        
        list(APPEND DOCUMENTED_MODULES ${MODULE})
        
        # Collect documentation-specific source files and header files
        get_target_property(DOC_SOURCES ${MODULE} DOCUMENTATION_SOURCES)
        get_target_property(DOC_HEADERS ${MODULE} DOCUMENTATION_HEADERS)
        get_target_property(DOC_INCLUDES ${MODULE} DOCUMENTATION_INCLUDE_DIRS)
        get_target_property(DOC_EXECUTABLE_FILES ${MODULE} DOCUMENTATION_EXECUTABLE_FILES)
        if(DOC_SOURCES)
            # If it's a comma-separated string, convert to list first
            string(REPLACE "," ";" DOC_SOURCES_LIST "${DOC_SOURCES}")
            message(STATUS "DOC_SOURCES_LIST = ${DOC_SOURCES_LIST}")
            list(APPEND ALL_SOURCE_DIRS ${DOC_SOURCES_LIST})
        endif()
          if(DOC_HEADERS)
            # If it's a comma-separated string, convert to list first
            string(REPLACE "," ";" DOC_HEADERS_LIST "${DOC_HEADERS}")
            message(STATUS "DOC_HEADERS_LIST = ${DOC_HEADERS_LIST}")
            list(APPEND ALL_INCLUDE_DIRS ${DOC_HEADERS_LIST})
        endif()
        
        if(DOC_INCLUDES)
            string(REPLACE "," ";" DOC_INCLUDES_LIST "${DOC_INCLUDES}")
            list(APPEND ALL_INCLUDE_DIRS ${DOC_INCLUDES_LIST})
        endif()
        if (DOC_EXECUTABLE_FILES)
            string(REPLACE "," ";" DOC_EXECUTABLE_FILES_STR "${DOC_EXECUTABLE_FILES}")
            list(APPEND ALL_EXECUTABLE_FILES ${DOC_EXECUTABLE_FILES_STR})
        endif()
        set(doxy_gen_dict "\"${MODULE}\": \"${CMAKE_CURRENT_BINARY_DIR}/doxygen/xml\"")
        list(APPEND DOXYGEN_OUTPUT_PATH_XML_DICT_STRING "${doxy_gen_dict}")
        set(doxy_gen_dict "\"${MODULE}\": \"${CMAKE_CURRENT_BINARY_DIR}/doxygen/html\"")
        list(APPEND DOXYGEN_OUTPUT_PATH_HTML_DICT_STRING "${doxy_gen_dict}")
        message(STATUS "Module ${MODULE} has documentation:")
        message(STATUS "  Sources: ${DOC_SOURCES}")
        message(STATUS "  Headers: ${DOC_HEADERS}")
        message(STATUS "  Includes: ${DOC_INCLUDES}")
        message(STATUS "  Executable Files: ${DOC_EXECUTABLE_FILES}")
    endif()

    # Collect source code and include directories
    get_target_property(MODULE_SOURCE_DIR ${MODULE} SOURCE_DIR)
    get_target_property(MODULE_BINARY_DIR ${MODULE} BINARY_DIR)    
    # Add include directories
    get_target_property(INCLUDE_DIRS ${MODULE} INTERFACE_INCLUDE_DIRECTORIES)

    if(INCLUDE_DIRS)
        foreach(INCLUDE_DIR IN LISTS INCLUDE_DIRS)
            list(APPEND ALL_INCLUDE_DIRS ${INCLUDE_DIR})
        endforeach()
    endif()
    
    # Add source directories
    if(EXISTS "${MODULE_SOURCE_DIR}/src")
        list(APPEND ALL_SOURCE_DIRS "${MODULE_SOURCE_DIR}/src")
    endif()
    if(EXISTS "${MODULE_SOURCE_DIR}/inc")
        list(APPEND ALL_SOURCE_DIRS "${MODULE_SOURCE_DIR}/inc")
    endif()
endforeach()

# Remove duplicates
list(REMOVE_DUPLICATES ALL_INCLUDE_DIRS)
list(REMOVE_DUPLICATES ALL_SOURCE_DIRS)
    

message(STATUS "ALL_INCLUDE_DIRS: ${ALL_INCLUDE_DIRS}")
message(STATUS "ALL_SOURCE_DIRS: ${ALL_SOURCE_DIRS}")

# ================================
# API Documentation (Doxygen)
# ================================
# Set Doxygen configuration variables
set(CMAKE_PROJECT_NAME ${PROJECT_NAME})
set(PROJECT_VERSION "1.0.0")
set(CMAKE_DOXYGEN_PROJECT_BRIEF "SIO Network Communication System Documentation")
set(CMAKE_DOXYGEN_PROJECT_LOGO "")
set(ZX_DOC_TARGET_OUTPUT_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}/doxygen)
set(ZX_DOC_HAVE_DOT "NO")
if(DOXYGEN_DOT_FOUND)
    set(ZX_DOC_HAVE_DOT "YES")
endif()

if(BUILD_API_DOCS AND DOXYGEN_FOUND)
    
    # Prepare input directory list
    set(DOXYGEN_INPUT_DIRS "")
    foreach(DIR IN LISTS ALL_INCLUDE_DIRS ALL_SOURCE_DIRS)
        if(EXISTS ${DIR})
            list(APPEND DOXYGEN_INPUT_DIRS ${DIR})
        endif()
    endforeach()

    foreach(EXEC_FILE IN LISTS ALL_EXECUTABLE_FILES)
        if(EXISTS ${EXEC_FILE})
            list(REMOVE_ITEM DOXYGEN_INPUT_DIRS ${EXEC_FILE})
            list(APPEND ZX_DOC_DOXYGEN_EXCLUDE ${EXEC_FILE})
            message(STATUS "Removed ${EXEC_FILE} from DOXYGEN_INPUT_DIRS")
        endif()
    endforeach()

    # Convert to space-separated string
    string(REPLACE ";" " " DOXYGEN_INPUT_STRING "${DOXYGEN_INPUT_DIRS}")    
    # Set Doxygen input path (after DOXYGEN_INPUT_STRING is defined)
    set(ZX_DOC_DOXYGEN_INPUT ${DOXYGEN_INPUT_STRING})    
    # Set exclude paths
    if(ZX_DOC_DOXYGEN_EXCLUDE)
        string(REPLACE ";" " " ZX_DOC_DOXYGEN_EXCLUDE_STRING "${ZX_DOC_DOXYGEN_EXCLUDE}")
        set(ZX_DOC_DOXYGEN_EXCLUDE ${ZX_DOC_DOXYGEN_EXCLUDE_STRING})
    else()
        set(ZX_DOC_DOXYGEN_EXCLUDE "")
    endif()
    
    

    set(DOXYGEN_PROJECT_NAME ${PROJECT_NAME})
    set(DOXYGEN_PROJECT_VERSION ${PROJECT_VERSION})
    set(DOXYGEN_PROJECT_DESCRIPTION ${PROJECT_DESCRIPTION})
    set(DOXYGEN_INPUT ${DOXYGEN_INPUT_STRING})
    set(DOXYGEN_OUTPUT_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}/doxygen)
    set(DOXYGEN_HTML_OUTPUT html)
    set(DOXYGEN_XML_OUTPUT xml)    
    # Configure Doxyfile
    configure_file(${CMAKE_CURRENT_LIST_DIR}/docs/Doxyfile.in ${CMAKE_CURRENT_BINARY_DIR}/Doxyfile @ONLY)    # Create API documentation target
    add_custom_target(api_docs
        COMMAND ${DOXYGEN_EXECUTABLE} ${CMAKE_CURRENT_BINARY_DIR}/Doxyfile
        WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
        COMMENT "Generating API documentation with Doxygen"
        VERBATIM    )
    
    # Set output directory properties
    set_target_properties(api_docs PROPERTIES
        DOC_OUTPUT_DIR ${DOXYGEN_OUTPUT_DIRECTORY}
    )
endif()

# ================================
# User Documentation (Sphinx)
# ================================
if(BUILD_USER_DOCS AND Python3_FOUND)    
    # Check Sphinx availability
    execute_process(
        COMMAND ${Python3_EXECUTABLE} -c "import sphinx; print(sphinx.__version__)"
        RESULT_VARIABLE SPHINX_RESULT
        OUTPUT_VARIABLE SPHINX_VERSION
        ERROR_QUIET
        OUTPUT_STRIP_TRAILING_WHITESPACE
    )
    
    # Check LaTeX availability for PDF generation
    find_program(XELATEX_EXECUTABLE xelatex)
    find_program(PDFLATEX_EXECUTABLE pdflatex)
    find_program(MAKEINDEX_EXECUTABLE makeindex)
    find_program(LATEXMK_EXECUTABLE latexmk)

    # Prefer XeLaTeX for better Unicode and Chinese support
    if(XELATEX_EXECUTABLE AND MAKEINDEX_EXECUTABLE)
        set(LATEX_AVAILABLE TRUE)
        set(LATEX_EXECUTABLE ${XELATEX_EXECUTABLE})
        message(STATUS "Found XeLaTeX tools: xelatex and makeindex")
        if(LATEXMK_EXECUTABLE)
            message(STATUS "Found latexmk: ${LATEXMK_EXECUTABLE}")
        endif()
    elseif(PDFLATEX_EXECUTABLE AND MAKEINDEX_EXECUTABLE)
        set(LATEX_AVAILABLE TRUE)
        set(LATEX_EXECUTABLE ${PDFLATEX_EXECUTABLE})
        message(STATUS "Found LaTeX tools: pdflatex and makeindex")
        if(LATEXMK_EXECUTABLE)
            message(STATUS "Found latexmk: ${LATEXMK_EXECUTABLE}")
        endif()
    else()
        set(LATEX_AVAILABLE FALSE)
        message(STATUS "LaTeX tools not found, PDF generation will be disabled")
    endif()
    
    if(SPHINX_RESULT EQUAL 0)        
        message(STATUS "Found Sphinx: ${SPHINX_VERSION}")
        
        # Set Sphinx configuration
        set(SPHINX_SOURCE_DIR ${CMAKE_CURRENT_SOURCE_DIR})
        set(SPHINX_BUILD_DIR ${CMAKE_CURRENT_BINARY_DIR}/sphinx)
        set(SPHINX_CACHE_DIR ${SPHINX_BUILD_DIR}/.doctrees)
        set(SPHINX_HTML_DIR ${SPHINX_BUILD_DIR}/html)
        set(SPHINX_PDF_DIR ${SPHINX_BUILD_DIR}/latex)
        set(SPHINX_EPUB_DIR ${SPHINX_BUILD_DIR}/epub)
        set(SPHINX_LATEX_DIR ${SPHINX_BUILD_DIR}/latex)

        # Set Breathe configuration (if API documentation exists)
        if(TARGET api_docs)
            set(BREATHE_XML_PATH ${DOXYGEN_OUTPUT_DIRECTORY}/${DOXYGEN_XML_OUTPUT})
        else()
            set(BREATHE_XML_PATH "")
        endif()

        # Configure conf.py
        # Set relative path for Doxygen output directory for Sphinx configuration
        # Save the original absolute path for Doxygen generation
        set(ZX_DOC_TARGET_OUTPUT_DIRECTORY_ABSOLUTE ${ZX_DOC_TARGET_OUTPUT_DIRECTORY})
        # Override with relative path for Sphinx configuration
        set(ZX_DOC_TARGET_OUTPUT_DIRECTORY "../doxygen")
        configure_file(${CMAKE_CURRENT_LIST_DIR}/docs/conf.py.in ${SPHINX_BUILD_DIR}/conf.py @ONLY)
        # Restore absolute path for other uses
        set(ZX_DOC_TARGET_OUTPUT_DIRECTORY ${ZX_DOC_TARGET_OUTPUT_DIRECTORY_ABSOLUTE})
        
        # Create HTML documentation target
        add_custom_target(user_docs_html
            COMMAND ${Python3_EXECUTABLE} -m sphinx
                -b html
                -d ${SPHINX_CACHE_DIR}
                -c ${SPHINX_BUILD_DIR}
                ${SPHINX_SOURCE_DIR}
                ${SPHINX_HTML_DIR}
            WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
            COMMENT "Building HTML user documentation with Sphinx"
            VERBATIM
        )
        
        # Create PDF documentation target (if LaTeX is available)
        if(BUILD_DOC_PDF AND LATEX_AVAILABLE)
            add_custom_target(user_docs_pdf
                COMMAND ${Python3_EXECUTABLE} -m sphinx
                    -b latex
                    -d ${SPHINX_CACHE_DIR}
                    -c ${SPHINX_BUILD_DIR}
                    ${SPHINX_SOURCE_DIR}
                    ${SPHINX_LATEX_DIR}
                COMMAND ${CMAKE_COMMAND} -E chdir ${SPHINX_LATEX_DIR}
                    ${LATEX_EXECUTABLE} -interaction=nonstopmode ${PROJECT_NAME}.tex || echo "Warning: LaTeX encountered errors but PDF may still be generated"
                COMMAND ${CMAKE_COMMAND} -E chdir ${SPHINX_LATEX_DIR}
                    ${LATEX_EXECUTABLE} -interaction=nonstopmode ${PROJECT_NAME}.tex || echo "Warning: LaTeX encountered errors but PDF may still be generated"
                WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
                COMMENT "Building PDF user documentation with Sphinx and LaTeX"
                VERBATIM
            )
            
            # If latexmk is available, use it for better PDF generation
            if(LATEXMK_EXECUTABLE)
                add_custom_target(user_docs_pdf_latexmk
                    COMMAND ${Python3_EXECUTABLE} -m sphinx
                        -b latex
                        -d ${SPHINX_CACHE_DIR}
                        -c ${SPHINX_BUILD_DIR}
                        ${SPHINX_SOURCE_DIR}
                        ${SPHINX_LATEX_DIR}
                    COMMAND ${CMAKE_COMMAND} -E chdir ${SPHINX_LATEX_DIR}
                        ${LATEXMK_EXECUTABLE} -pdf -f -interaction=nonstopmode ${PROJECT_NAME}.tex || echo "Warning: latexmk completed with warnings"
                    WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
                    COMMENT "Building PDF user documentation with Sphinx and latexmk"
                    VERBATIM
                )
            endif()
        endif()
        
        # Create EPUB documentation target
        if(BUILD_DOC_EPUB)
            add_custom_target(user_docs_epub
                COMMAND ${Python3_EXECUTABLE} -m sphinx
                    -b epub
                    -d ${SPHINX_CACHE_DIR}
                    -c ${SPHINX_BUILD_DIR}
                    ${SPHINX_SOURCE_DIR}
                    ${SPHINX_EPUB_DIR}
                WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
                COMMENT "Building EPUB user documentation with Sphinx"
                VERBATIM
            )
        endif()
        
        # Create LaTeX documentation target
        if(BUILD_DOC_LATEX)
            add_custom_target(user_docs_latex
                COMMAND ${Python3_EXECUTABLE} -m sphinx
                    -b latex
                    -d ${SPHINX_CACHE_DIR}
                    -c ${SPHINX_BUILD_DIR}
                    ${SPHINX_SOURCE_DIR}
                    ${SPHINX_LATEX_DIR}
                WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
                COMMENT "Building LaTeX user documentation with Sphinx"
                VERBATIM
            )
        endif()
        
        # Create unified user_docs target
        add_custom_target(user_docs
            COMMENT "Building user documentation in multiple formats"
        )
        
        # Add dependencies for user_docs
        add_dependencies(user_docs user_docs_html)
        
        if(TARGET user_docs_pdf)
            add_dependencies(user_docs user_docs_pdf)
        endif()
        
        if(TARGET user_docs_epub)
            add_dependencies(user_docs user_docs_epub)
        endif()
        
        if(TARGET user_docs_latex)
            add_dependencies(user_docs user_docs_latex)
        endif()
        
        # If API documentation exists, establish dependency relationship
        if(TARGET api_docs)
            add_dependencies(user_docs_html api_docs)
            if(TARGET user_docs_pdf)
                add_dependencies(user_docs_pdf api_docs)
            endif()
            if(TARGET user_docs_epub)
                add_dependencies(user_docs_epub api_docs)
            endif()
            if(TARGET user_docs_latex)
                add_dependencies(user_docs_latex api_docs)
            endif()
        endif()
        
        set_target_properties(user_docs PROPERTIES
            DOC_OUTPUT_DIR ${SPHINX_HTML_DIR}
        )
    else()
        message(STATUS "Sphinx not found, user documentation will not be built")
    endif()
endif()

# ================================
# Unified Documentation Target
# ================================
add_custom_target(docs
    COMMENT "Building all documentation"
)

# Add dependencies
if(TARGET api_docs)
    add_dependencies(docs api_docs)
endif()

if(TARGET user_docs)
    add_dependencies(docs user_docs)
endif()

# ================================
# Cleanup Target
# ================================
add_custom_target(clean-sphinx
    COMMAND ${CMAKE_COMMAND} -E remove_directory ${CMAKE_CURRENT_BINARY_DIR}/doxygen
    COMMAND ${CMAKE_COMMAND} -E remove_directory ${CMAKE_CURRENT_BINARY_DIR}/sphinx
    COMMENT "Cleaning documentation build directories"
)

set(EXPORT_DOCS_DIR "${CMAKE_CURRENT_BINARY_DIR}/../share" )
add_custom_command(
    TARGET docs POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E copy_directory ${CMAKE_CURRENT_BINARY_DIR}/doxygen ${EXPORT_DOCS_DIR}/doxygen
    COMMAND ${CMAKE_COMMAND} -E copy_directory ${CMAKE_CURRENT_BINARY_DIR}/sphinx/html ${EXPORT_DOCS_DIR}/sphinx/html
    COMMAND ${CMAKE_COMMAND} -E make_directory ${EXPORT_DOCS_DIR}/sphinx/pdf
    COMMAND ${CMAKE_COMMAND} -E copy_if_different ${CMAKE_CURRENT_BINARY_DIR}/sphinx/latex/${PROJECT_NAME}.pdf ${EXPORT_DOCS_DIR}/sphinx/pdf/${PROJECT_NAME}.pdf || echo "PDF not found, skipping..."
    COMMAND ${CMAKE_COMMAND} -E copy_directory ${CMAKE_CURRENT_BINARY_DIR}/sphinx/epub ${EXPORT_DOCS_DIR}/sphinx/epub || echo "EPUB not found, skipping..."
    COMMENT "Exporting documentation to ${EXPORT_DOCS_DIR}"
)
