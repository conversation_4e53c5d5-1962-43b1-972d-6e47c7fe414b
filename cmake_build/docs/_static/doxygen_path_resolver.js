/**
 * Doxygen路径解析器
 * 自动计算从当前页面到Doxygen文档的正确相对路径
 */

(function() {
    'use strict';

    /**
     * 计算从当前路径到doxygen目录的相对路径
     */
    function calculateDoxygenBasePath() {
        // 获取当前页面路径
        const currentPath = window.location.pathname;
        console.log('当前路径:', currentPath);
        
        // 查找/share/sphinx/html/模式
        const shareSphinxPattern = '/share/sphinx/html/';
        const shareIndex = currentPath.indexOf(shareSphinxPattern);
        
        if (shareIndex === -1) {
            console.warn('无法找到/share/sphinx/html/路径，使用默认路径');
            return '../../../doxygen';
        }
        
        // 提取从/share/sphinx/html/之后的路径部分
        const pathAfterSphinx = currentPath.substring(shareIndex + shareSphinxPattern.length);
        console.log('Sphinx后的路径:', pathAfterSphinx);
        
        // 计算需要多少个../来回到sphinx/html/目录
        const pathSegments = pathAfterSphinx.split('/').filter(segment => segment.length > 0);
        
        // 移除最后一个段（文件名）
        if (pathSegments.length > 0 && pathSegments[pathSegments.length - 1].endsWith('.html')) {
            pathSegments.pop();
        }
        
        console.log('路径段数:', pathSegments.length, '段内容:', pathSegments);
        
        // 生成相对路径：
        // 1. 从当前目录回到 /share/sphinx/html/ 目录需要 pathSegments.length 个 ../ 
        // 2. 从 /share/sphinx/html/ 回到 /share/ 需要 2 个 ../ (html -> sphinx -> share)
        // 3. 从 /share/ 进入 doxygen/ 目录
        const upLevelsToSphinxHtml = pathSegments.length;
        const upLevelsToShare = 2; // html -> sphinx -> share
        const totalUpLevels = upLevelsToSphinxHtml + upLevelsToShare;
        const relativePath = '../'.repeat(totalUpLevels) + 'doxygen';
        
        console.log('从当前目录到sphinx/html需要回退:', upLevelsToSphinxHtml);
        console.log('从sphinx/html到share需要回退:', upLevelsToShare);
        console.log('总共需要回退层级:', totalUpLevels);
        console.log('计算得出的Doxygen基础路径:', relativePath);
        return relativePath;
    }

    /**
     * 动态生成C++名称到Doxygen HTML文件名的映射
     */
    let cppNameMapping = {};

    /**
     * 从Doxygen XML数据动态创建完整映射表
     */
    async function generateDynamicMapping() {
        try {
            const basePath = calculateDoxygenBasePath();
            console.log('开始从Doxygen XML数据生成完整映射表...');
            
            // 首先尝试从Doxygen XML index.xml获取完整映射
            const xmlMappings = await parseDoxygenXMLIndex(basePath);
            
            if (Object.keys(xmlMappings).length > 0) {
                cppNameMapping = xmlMappings;
                console.log(`从XML成功加载 ${Object.keys(xmlMappings).length} 个映射条目`);
            } else {
                // XML解析失败时的最小备用映射
                console.warn('XML解析失败，使用最小备用映射');
                cppNameMapping = await getMinimalFallbackMapping();
            }
            
            // 尝试从当前页面发现额外映射
            await discoverAdditionalMappings(basePath);
            
            console.log('最终映射表条目数:', Object.keys(cppNameMapping).length);
            
        } catch (error) {
            console.error('动态映射生成失败:', error);
            cppNameMapping = await getMinimalFallbackMapping();
        }
    }

    /**
     * 解析Doxygen XML index.xml文件获取完整映射
     */
    async function parseDoxygenXMLIndex(basePath) {
        try {
            // 尝试多个可能的XML路径
            const xmlPaths = [
                `${basePath}/xml/index.xml`,
                `${basePath}/../xml/index.xml`,
                '../xml/index.xml',
                '../../xml/index.xml',
                '../../../xml/index.xml'
            ];
            
            for (const xmlPath of xmlPaths) {
                try {
                    console.log('尝试读取XML:', xmlPath);
                    const response = await fetch(xmlPath);
                    if (response.ok) {
                        const xmlText = await response.text();
                        const mappings = parseXMLContent(xmlText);
                        if (Object.keys(mappings).length > 0) {
                            console.log(`成功从 ${xmlPath} 解析映射`);
                            return mappings;
                        }
                    }
                } catch (err) {
                    console.log(`无法读取 ${xmlPath}:`, err.message);
                }
            }
            
            return {};
            
        } catch (error) {
            console.warn('解析Doxygen XML失败:', error);
            return {};
        }
    }

    /**
     * 解析XML内容提取映射关系
     */
    function parseXMLContent(xmlText) {
        try {
            const parser = new DOMParser();
            const xmlDoc = parser.parseFromString(xmlText, 'text/xml');
            const compounds = xmlDoc.querySelectorAll('compound');
            const mappings = {};
            
            compounds.forEach(compound => {
                const kind = compound.getAttribute('kind');
                const refid = compound.getAttribute('refid');
                const nameElement = compound.querySelector('name');
                
                if (nameElement && refid && (kind === 'struct' || kind === 'class' || kind === 'group')) {
                    const cppName = nameElement.textContent.trim();
                    
                    // 从refid提取Doxygen文件名
                    let doxygenName = '';
                    
                    if (kind === 'struct' && refid.startsWith('struct')) {
                        // 结构体: structxxx -> xxx (去掉struct前缀)
                        doxygenName = refid.substring(6); // 移除 'struct' 前缀
                    } else if (kind === 'class' && refid.startsWith('class')) {
                        // 类: classxxx -> xxx (去掉class前缀)  
                        doxygenName = refid.substring(5); // 移除 'class' 前缀
                    } else if (kind === 'group' && refid.startsWith('group__')) {
                        // 组: group__xxx -> xxx (去掉group__前缀)
                        doxygenName = refid.substring(7); // 移除 'group__' 前缀
                    }
                    
                    if (doxygenName && cppName) {
                        mappings[cppName] = doxygenName;
                        console.log(`映射: ${cppName} -> ${doxygenName}`);
                    }
                }
            });
            
            return mappings;
            
        } catch (error) {
            console.error('解析XML内容失败:', error);
            return {};
        }
    }

    /**
     * 获取最小备用映射（仅包含已知工作的条目）
     */
    async function getMinimalFallbackMapping() {
        return {
            // // 核心结构体
            // 'CANMsg_E2E_Cfg': '_c_a_n_msg___e2_e___cfg',
            // 'CANRxSegment_E2E_Info': '_c_a_n_rx_segment___e2_e___info', 
            // 'CANRxMsg_E2E_Cfg': '_c_a_n_rx_msg___e2_e___cfg',
            // 'CANSegment_E2E_Info': '_c_a_n_segment___e2_e___info',
            
            // // 核心类
            // 'sio_net::NetMode': 'sio__net_1_1_net_mode',
            // 'sio_net::BusoffMode': 'sio__net_1_1_busoff_mode',
            // 'sio_net::CanMsgTemp': 'sio__net_1_1_can_msg_temp',
            // 'sio_net::MultiValueType': 'sio__net_1_1_multi_value_type',
            
            // // 核心API组
            // 'SIO_NET_API': '_s_i_o___n_e_t___a_p_i',
            // 'NET_MODE_API': '_n_e_t___m_o_d_e___a_p_i',
            // 'SIGNAL_PROCESS_API': '_s_i_g_n_a_l___p_r_o_c_e_s_s___a_p_i',
            // 'NET_MONITOR_API': '_n_e_t___m_o_n_i_t_o_r___a_p_i',
            // 'DIAG_TIME_API': '_d_i_a_g___t_i_m_e___a_p_i',
            // 'RUN_REQ_API': '_r_u_n___r_e_q___a_p_i',
            // 'MSG_VALID_API': '_m_s_g___v_a_l_i_d___a_p_i',
            // 'E2E_PROTECT_API': '_e2_e___p_r_o_t_e_c_t___a_p_i',
            // 'SIGNAL_CHECK_API': '_s_i_g_n_a_l___c_h_e_c_k___a_p_i'
        };
    }

    /**
     * 发现额外的映射关系
     */
    async function discoverAdditionalMappings(basePath) {
        try {
            // 尝试检查页面中的链接，从href中提取映射关系
            const links = document.querySelectorAll('a[href*="struct"], a[href*="class"]');
            
            links.forEach(link => {
                const href = link.getAttribute('href');
                const text = link.textContent.trim();
                
                if (href && text) {
                    // 从href中提取doxygen名称
                    const structMatch = href.match(/struct([^.]+)\.html/);
                    const classMatch = href.match(/class([^.]+)\.html/);
                    
                    if (structMatch && text && !text.includes('::')) {
                        // 结构体映射：去掉前缀的下划线
                        const doxygenName = structMatch[1];
                        if (doxygenName.startsWith('_')) {
                            const cleanName = doxygenName.substring(1);
                            // 尝试反向工程C++名称
                            const cppName = convertDoxygenToCppName(cleanName, text);
                            if (cppName && !cppNameMapping[cppName]) {
                                cppNameMapping[cppName] = cleanName;
                                console.log('发现结构体映射:', cppName, '->', cleanName);
                            }
                        }
                    } else if (classMatch) {
                        // 类映射
                        const doxygenName = classMatch[1];
                        const cppName = convertDoxygenToCppName(doxygenName, text);
                        if (cppName && !cppNameMapping[cppName]) {
                            cppNameMapping[cppName] = doxygenName;
                            console.log('发现类映射:', cppName, '->', doxygenName);
                        }
                    }
                }
            });
            
        } catch (error) {
            console.warn('发现额外映射时出错:', error);
        }
    }

    /**
     * 将Doxygen名称转换为可能的C++名称
     */
    function convertDoxygenToCppName(doxygenName, linkText) {
        try {
            // 如果链接文本看起来像C++名称，直接使用
            if (linkText && /^[a-zA-Z_][a-zA-Z0-9_:]*$/.test(linkText)) {
                return linkText;
            }
            
            // 尝试将doxygen名称转换回C++名称
            let cppName = doxygenName;
            
            // 处理命名空间分隔符 (1_1 -> ::)
            cppName = cppName.replace(/_1_1/g, '::');
            
            // 处理下划线分隔的大写字母
            cppName = cppName.replace(/_([a-z])/g, (match, letter) => letter.toUpperCase());
            
            // 移除多余的下划线
            cppName = cppName.replace(/^_+|_+$/g, '');
            cppName = cppName.replace(/__+/g, '_');
            
            return cppName;
            
        } catch (error) {
            console.warn('转换Doxygen名称时出错:', error);
            return null;
        }
    }

    /**
     * 替换页面中所有的DOXYGEN_BASE_PATH占位符
     */
    function replacePlaceholders() {
        const basePath = calculateDoxygenBasePath();
        
        // 查找所有包含DOXYGEN_BASE_PATH的链接
        const links = document.querySelectorAll('a[href*="DOXYGEN_BASE_PATH"]');
        
        links.forEach(link => {
            let originalHref = link.getAttribute('href');
            let newHref = originalHref.replace('DOXYGEN_BASE_PATH', basePath);
            
            // 处理友好名称映射
            Object.keys(cppNameMapping).forEach(cppName => {
                const doxygenName = cppNameMapping[cppName];
                // 查找并替换struct{CppName}.html模式
                const structPattern = new RegExp(`struct${cppName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\.html`, 'g');
                const classPattern = new RegExp(`class${cppName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\.html`, 'g');
                const groupPattern = new RegExp(`group${cppName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\.html`, 'g');
                
                newHref = newHref.replace(structPattern, `struct${doxygenName}.html`);
                newHref = newHref.replace(classPattern, `class${doxygenName}.html`);
                newHref = newHref.replace(groupPattern, `group__${doxygenName}.html`);
            });
            
            link.setAttribute('href', newHref);
            
            // 添加标识类，方便样式控制
            link.classList.add('doxygen-link');
            
            console.log('更新链接:', originalHref, '->', newHref);
        });
        
        console.log(`总共更新了 ${links.length} 个Doxygen链接`);
    }

    /**
     * 验证Doxygen链接是否可访问（可选）
     */
    function validateDoxygenLinks() {
        const doxygenLinks = document.querySelectorAll('a.doxygen-link');
        
        doxygenLinks.forEach(link => {
            const href = link.getAttribute('href');
            
            // 添加点击事件监听，用于调试
            link.addEventListener('click', function() {
                console.log('点击Doxygen链接:', href);
                
                // 可以在这里添加链接验证逻辑
                // 例如检查目标文件是否存在
            });
        });
    }

    // 页面加载完成后执行
    document.addEventListener('DOMContentLoaded', async function() {
        console.log('开始处理Doxygen链接...');
        
        // 首先生成动态映射
        await generateDynamicMapping();
        
        // 延迟执行，确保所有内容都已加载
        setTimeout(() => {
            replacePlaceholders();
            validateDoxygenLinks();
        }, 100);
    });

    // 也在window.onload时执行一次，以防DOMContentLoaded已经触发
    window.addEventListener('load', async function() {
        if (!document.querySelector('a.doxygen-link')) {
            console.log('Window onload: 重新处理Doxygen链接...');
            
            // 确保映射已生成
            if (Object.keys(cppNameMapping).length === 0) {
                await generateDynamicMapping();
            }
            
            setTimeout(() => {
                replacePlaceholders();
                validateDoxygenLinks();
            }, 100);
        }
    });

})();