/* 
 * Doxygen链接样式
 * 为Sphinx文档中的Doxygen链接提供特殊样式
 */

/* Doxygen外部链接的基本样式 */
.reference.external[href*="doxygen/html"] {
    color: #0066cc;
    text-decoration: none;
    border-bottom: 1px dotted #0066cc;
    font-weight: 500;
}

.reference.external[href*="doxygen/html"]:hover {
    color: #004499;
    text-decoration: none;
    border-bottom: 1px solid #004499;
    background-color: #f0f8ff;
    padding: 1px 2px;
    border-radius: 2px;
}

/* 为不同类型的Doxygen链接提供不同的图标 */
.reference.external[href*="struct"]:before {
    content: "📋 ";
    font-size: 0.8em;
}

.reference.external[href*="class"]:before {
    content: "🏗️ ";
    font-size: 0.8em;
}

.reference.external[href*="group"]:before {
    content: "📁 ";
    font-size: 0.8em;
}

/* Breathe生成的结构体链接特殊样式 */
.breathe-sectiondef .sig-name {
    font-family: 'Consolas', 'Monaco', 'Menlo', monospace;
}

/* 为Doxygen链接添加提示文本 */
.reference.external[href*="doxygen/html"]:after {
    content: " (跳转到详细API文档)";
    font-size: 0.75em;
    color: #666;
    font-style: italic;
}

/* 在深色主题下的样式调整 */
@media (prefers-color-scheme: dark) {
    .reference.external[href*="doxygen/html"] {
        color: #66b3ff;
        border-bottom-color: #66b3ff;
    }
    
    .reference.external[href*="doxygen/html"]:hover {
        color: #99ccff;
        border-bottom-color: #99ccff;
        background-color: #1a1a2e;
    }
    
    .reference.external[href*="doxygen/html"]:after {
        color: #aaa;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .reference.external[href*="doxygen/html"]:after {
        display: none; /* 在小屏幕上隐藏提示文本 */
    }
}
