# Configuration file for the Sphinx documentation builder.
#
# This file only contains a selection of the most common options. For a full
# list see the documentation:
# https://www.sphinx-doc.org/en/master/usage/configuration.html

# -- Path setup --------------------------------------------------------------

# If extensions (or modules to document with autodoc) are in another directory,
# add these directories to sys.path here. If the directory is relative to the
# documentation root, use os.path.abspath to make it absolute, like shown here.
#
import os
import sys
# sys.path.insert(0, os.path.abspath('.'))


# -- Project information -----------------------------------------------------

project = '@PROJECT_NAME@'
copyright = '@PROJECT_YEAR@, @PROJECT_AUTHORS@'
author = '@PROJECT_AUTHORS@'


# -- General configuration ---------------------------------------------------

# Add any Sphinx extension module names here, as strings. They can be
# extensions coming with Sphinx (named 'sphinx.ext.*') or your custom
# ones.
extensions = [
    "breathe",
    'sphinxcontrib.plantuml',
    'sphinx.ext.imgmath',  # For mathematical expressions in LaTeX/PDF
    'sphinx.ext.todo',     # For TODO items
    'sphinx.ext.viewcode', # For source code links
    'sphinx.ext.extlinks', # For external links to Doxygen
]

plantuml = 'java -Djava.awt.headless=true -jar /usr/share/plantuml/plantuml.jar'

plantuml_output_format = "svg_img"

# Add any paths that contain templates here, relative to this directory.
templates_path = ['_templates']

# List of patterns, relative to source directory, that match files and
# directories to ignore when looking for source files.
# This pattern also affects html_static_path and html_extra_path.
exclude_patterns = ['_build', 'Thumbs.db', '.DS_Store']


# -- Options for HTML output -------------------------------------------------

# The theme to use for HTML and HTML Help pages.  See the documentation for
# a list of builtin themes.
#
html_theme = 'sphinx_rtd_theme'

# Add any paths that contain custom static files (such as style sheets) here,
# relative to this directory. They are copied after the builtin static files,
# so a file named "default.css" will overwrite the builtin "default.css".
html_static_path = []

# -- Breath configuration ---------------------------------------------------
breathe_projects = {
    @DOXYGEN_OUTPUT_PATH_XML_DICT_STRING@
}

breathe_default_project = "@PROJECT_NAME@"

# 配置Breathe生成到Doxygen HTML的链接
breathe_doxygen_config_options = {
    'GENERATE_HTML': 'YES',
    'HTML_OUTPUT': 'html',
    'GENERATE_XML': 'YES',
    'XML_OUTPUT': 'xml',
    'HTML_FILE_EXTENSION': '.html'
}

# 配置Breathe自动链接到Doxygen HTML
breathe_domain_by_extension = {
    "h": "cpp",
    "hpp": "cpp",
    "cpp": "cpp",
    "cxx": "cpp",
}

# 配置Breathe直接链接到Doxygen HTML
# 这将使Breathe生成的内容直接链接到Doxygen HTML页面
breathe_doxygen_html_base_path = ''

# 启用Breathe到Doxygen HTML的自动链接
breathe_show_define_initializer = True
breathe_show_enumvalue_initializer = True
breathe_show_include = False

# 为doxygenstruct等指令启用HTML链接
breathe_link_to_doxygen = True

# 配置外部链接扩展，支持手动链接到Doxygen HTML
# 使用占位符，稍后通过JavaScript动态替换为正确路径
extlinks = {
    'doxyfile': ('DOXYGEN_BASE_PATH/html/%s.html', '%s'),
    'doxystruct': ('DOXYGEN_BASE_PATH/html/struct%s.html', '%s'),
    'doxyclass': ('DOXYGEN_BASE_PATH/html/class%s.html', '%s'),
    'doxyenum': ('DOXYGEN_BASE_PATH/html/group__%s.html', '%s'),
    'doxygroup': ('DOXYGEN_BASE_PATH/html/group__%s.html', '%s'),
    # 友好的结构体链接，支持直接使用C++名称
    'struct': ('DOXYGEN_BASE_PATH/html/struct%s.html', '%s'),
    'class': ('DOXYGEN_BASE_PATH/html/class%s.html', '%s'),
}

# 配置HTML主题选项，添加对外部链接的样式支持
html_theme_options = {
    'style_external_links': True,
}

# 自定义CSS文件路径
html_static_path = ['_static']

# 确保CSS文件被复制
import os
import shutil

def copy_static_files(app, config):
    """复制静态文件到输出目录"""
    # 静态文件源目录应该在cmake_build/docs/_static
    static_src = os.path.join(os.path.dirname(__file__), '_static')
    static_dst = os.path.join(app.outdir, '_static')
    
    print(f"复制静态文件: {static_src} -> {static_dst}")
    print(f"当前配置文件位置: {__file__}")
    print(f"Sphinx源目录: {app.srcdir}")
    print(f"Sphinx输出目录: {app.outdir}")
    
    if os.path.exists(static_src):
        if not os.path.exists(static_dst):
            os.makedirs(static_dst, exist_ok=True)
        
        # 复制CSS文件
        doxygen_css = os.path.join(static_src, 'doxygen_links.css')
        if os.path.exists(doxygen_css):
            dst_css = os.path.join(static_dst, 'doxygen_links.css')
            shutil.copy2(doxygen_css, dst_css)
            print(f"复制CSS文件: {doxygen_css} -> {dst_css}")
        
        # 复制JavaScript文件
        doxygen_js = os.path.join(static_src, 'doxygen_path_resolver.js')
        if os.path.exists(doxygen_js):
            dst_js = os.path.join(static_dst, 'doxygen_path_resolver.js')
            shutil.copy2(doxygen_js, dst_js)
            print(f"复制JavaScript文件: {doxygen_js} -> {dst_js}")
    else:
        print(f"静态文件源目录不存在: {static_src}")
        # 尝试列出可能的位置
        possible_paths = [
            os.path.join(app.srcdir, '_static'),
            os.path.join(app.srcdir, '../_static'),
            '/home/<USER>/code/ifc2p_imo_arapp/cmake_build/docs/_static'
        ]
        for path in possible_paths:
            print(f"检查路径: {path} - 存在: {os.path.exists(path)}")
            if os.path.exists(path):
                static_src = path
                print(f"使用静态文件源目录: {static_src}")
                
                if not os.path.exists(static_dst):
                    os.makedirs(static_dst, exist_ok=True)
                
                # 复制CSS文件
                doxygen_css = os.path.join(static_src, 'doxygen_links.css')
                if os.path.exists(doxygen_css):
                    dst_css = os.path.join(static_dst, 'doxygen_links.css')
                    shutil.copy2(doxygen_css, dst_css)
                    print(f"复制CSS文件: {doxygen_css} -> {dst_css}")
                
                # 复制JavaScript文件
                doxygen_js = os.path.join(static_src, 'doxygen_path_resolver.js')
                if os.path.exists(doxygen_js):
                    dst_js = os.path.join(static_dst, 'doxygen_path_resolver.js')
                    shutil.copy2(doxygen_js, dst_js)
                    print(f"复制JavaScript文件: {doxygen_js} -> {dst_js}")
                break

# 自定义setup函数，用于添加CSS和处理Doxygen链接
def setup(app):
    """自定义Sphinx设置，添加对Doxygen链接的支持"""
    
    # 添加自定义CSS
    app.add_css_file('doxygen_links.css')
    
    # 添加JavaScript路径解析器
    app.add_js_file('doxygen_path_resolver.js')
    
    # 添加配置值
    app.add_config_value('doxygen_html_path', 'DOXYGEN_BASE_PATH/html/', 'env')
    
    # 连接静态文件复制事件
    app.connect('config-inited', copy_static_files)
    
    return {
        'version': '1.0',
        'parallel_read_safe': True,
        'parallel_write_safe': True,
    }

# -- LaTeX output configuration ---------------------------------------------

# Use XeLaTeX for better Unicode and font support
latex_engine = 'xelatex'

latex_elements = {
    # The paper size ('letterpaper' or 'a4paper').
    'papersize': 'a4paper',

    # The font size ('10pt', '11pt' or '12pt').
    'pointsize': '11pt',

    # Additional stuff for the LaTeX preamble.
    'preamble': r'''
% Basic packages for Chinese support
\usepackage{fontspec}
\usepackage{xeCJK}

% Force set Chinese fonts (ensure compatibility)
\setCJKmainfont{Noto Sans CJK SC}[
    BoldFont = Noto Sans CJK SC Bold,
    ItalicFont = Noto Sans CJK SC,
    BoldItalicFont = Noto Sans CJK SC Bold
]

% Fix head height warning and fancyhdr setup
\usepackage{fancyhdr}
\setlength{\headheight}{15pt}
\addtolength{\topmargin}{-3pt}

% Redefine fancyhdr to avoid Chinese character issues
\fancypagestyle{normal}{
  \fancyhf{}
  \fancyhead[LE,RO]{\thepage}
  \fancyhead[LO,RE]{\leftmark}
  \renewcommand{\headrulewidth}{0.4pt}
  \renewcommand{\footrulewidth}{0pt}
}
\fancypagestyle{plain}{
  \fancyhf{}
  \fancyfoot[C]{\thepage}
  \renewcommand{\headrulewidth}{0pt}
  \renewcommand{\footrulewidth}{0pt}
}

% Additional fixes for Chinese character processing
\XeTeXlinebreaklocale "zh"
\XeTeXlinebreakskip = 0pt plus 1pt minus 0.1pt

% Prevent math mode issues with Chinese characters
\usepackage{amsmath}
\usepackage{amssymb}
\usepackage{amsfonts}

% Additional packages for better formatting
\usepackage{indentfirst}
\setlength{\parindent}{2em}
''',

    # Latex figure (float) alignment
    'figure_align': 'htbp',
}

# Grouping the document tree into LaTeX files. List of tuples
# (source start file, target name, title, author, documentclass [howto, manual, or own class]).
latex_documents = [
    ('index', '@PROJECT_NAME@.tex', '@PROJECT_NAME@ Documentation',
     '@PROJECT_AUTHORS@', 'manual'),
]

# -- EPUB output configuration ----------------------------------------------

epub_title = '@PROJECT_NAME@'
epub_author = '@PROJECT_AUTHORS@'
epub_publisher = '@PROJECT_AUTHORS@'
epub_copyright = '@PROJECT_YEAR@, @PROJECT_AUTHORS@'

# The unique identifier of the text. This can be an ISBN number
# or the project homepage.
epub_identifier = '@PROJECT_NAME@'

# A unique identification for the text.
epub_uid = '@PROJECT_NAME@'

# HTML files that should be inserted before the pages created by sphinx.
epub_pre_files = []

# HTML files that should be inserted after the pages created by sphinx.
epub_post_files = []

# A list of files that should not be packed into the epub file.
epub_exclude_files = ['search.html']

# -- PDF configuration via rst2pdf ------------------------------------------

# PDF output via rst2pdf (alternative to LaTeX)
# Uncomment and configure if you prefer rst2pdf over LaTeX
# pdf_documents = [
#     ('index', '@PROJECT_NAME@', '@PROJECT_NAME@ Documentation', '@PROJECT_AUTHORS@'),
# ]

# -- PlantUML configuration -------------------------------------------------

# PlantUML configuration for different output formats
plantuml_output_format = 'svg_img'

# For LaTeX/PDF output, use PNG format for PlantUML
if 'latex' in sys.argv or 'latexpdf' in sys.argv:
    plantuml_output_format = 'png'
