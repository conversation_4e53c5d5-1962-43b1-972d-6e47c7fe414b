#!/bin/bash

# Sphinx 文档重新构建脚本
# 用于重新生成Sphinx文档，确保Doxygen链接正常工作

set -e  # 遇到错误立即停止

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Sphinx 文档重新构建脚本 ===${NC}"

# 检查当前目录
if [[ ! -f "CMakeLists.txt" ]]; then
    echo -e "${RED}错误: 请在项目根目录运行此脚本${NC}"
    exit 1
fi

# 设置构建目录
BUILD_DIR="build/cpj_chery_icar05"
DOCS_BUILD_DIR="${BUILD_DIR}/docs"

echo -e "${YELLOW}1. 清理旧的文档构建文件...${NC}"
if [[ -d "${DOCS_BUILD_DIR}" ]]; then
    rm -rf "${DOCS_BUILD_DIR}/doxygen"
    rm -rf "${DOCS_BUILD_DIR}/sphinx"
    echo -e "${GREEN}   ✓ 已清理旧文档${NC}"
fi

echo -e "${YELLOW}2. 创建构建目录...${NC}"
mkdir -p "${BUILD_DIR}"
cd "${BUILD_DIR}"

echo -e "${YELLOW}3. 运行CMake配置...${NC}"
cmake ../.. -DBUILD_DOCS=ON -DBUILD_API_DOCS=ON -DBUILD_USER_DOCS=ON

echo -e "${YELLOW}4. 生成Doxygen API文档...${NC}"
make api_docs

echo -e "${YELLOW}5. 生成Sphinx用户文档...${NC}"
make user_docs_html

echo -e "${YELLOW}6. 检查生成的文档...${NC}"

# 检查Doxygen HTML
if [[ -f "docs/doxygen/html/index.html" ]]; then
    echo -e "${GREEN}   ✓ Doxygen HTML文档生成成功${NC}"
else
    echo -e "${RED}   ✗ Doxygen HTML文档生成失败${NC}"
fi

# 检查Sphinx HTML
if [[ -f "docs/sphinx/html/index.html" ]]; then
    echo -e "${GREEN}   ✓ Sphinx HTML文档生成成功${NC}"
else
    echo -e "${RED}   ✗ Sphinx HTML文档生成失败${NC}"
fi

# 检查结构体文档
TARGET_STRUCT="docs/doxygen/html/struct_c_a_n_rx_msg___e2_e___cfg.html"
if [[ -f "${TARGET_STRUCT}" ]]; then
    echo -e "${GREEN}   ✓ 目标结构体文档存在: ${TARGET_STRUCT}${NC}"
else
    echo -e "${RED}   ✗ 目标结构体文档不存在: ${TARGET_STRUCT}${NC}"
fi

echo -e "${YELLOW}7. 生成文档链接报告...${NC}"

# 生成链接检查报告
REPORT_FILE="docs_link_report.txt"
cat > "${REPORT_FILE}" << EOF
=== 文档链接检查报告 ===
生成时间: $(date)

1. Doxygen HTML文档位置:
   $(pwd)/docs/doxygen/html/

2. Sphinx HTML文档位置:
   $(pwd)/docs/sphinx/html/

3. 主要结构体文档:
EOF

# 检查主要结构体文档是否存在
STRUCTS=(
    "struct_c_a_n_msg___e2_e___cfg.html"
    "struct_c_a_n_rx_msg___e2_e___cfg.html"
    "struct_c_a_n_rx_segment___e2_e___info.html"
)

for struct in "${STRUCTS[@]}"; do
    if [[ -f "docs/doxygen/html/${struct}" ]]; then
        echo "   ✓ ${struct}" >> "${REPORT_FILE}"
    else
        echo "   ✗ ${struct}" >> "${REPORT_FILE}"
    fi
done

echo >> "${REPORT_FILE}"
echo "4. 主要类文档:" >> "${REPORT_FILE}"

CLASSES=(
    "classsio__net_1_1_net_mode.html"
    "classsio__net_1_1_busoff_mode.html"
    "classsio__net_1_1_can_msg_temp.html"
)

for class in "${CLASSES[@]}"; do
    if [[ -f "docs/doxygen/html/${class}" ]]; then
        echo "   ✓ ${class}" >> "${REPORT_FILE}"
    else
        echo "   ✗ ${class}" >> "${REPORT_FILE}"
    fi
done

echo -e "${GREEN}   ✓ 报告已保存到: ${BUILD_DIR}/${REPORT_FILE}${NC}"

echo -e "${BLUE}=== 构建完成 ===${NC}"
echo -e "${GREEN}请打开以下链接查看文档:${NC}"
echo -e "  Sphinx文档: file://$(pwd)/docs/sphinx/html/index.html"
echo -e "  Doxygen文档: file://$(pwd)/docs/doxygen/html/index.html"
echo -e "${YELLOW}注意: 请检查Sphinx文档中的Doxygen链接是否正常工作${NC}"
