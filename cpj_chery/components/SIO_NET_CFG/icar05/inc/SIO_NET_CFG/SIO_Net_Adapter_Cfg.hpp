/**
 * @file SIO_Net_Adapter_Cfg.hpp
 * @brief SIO Network Adapter Configuration for ICAR05 Platform
 *
 * This file contains platform-specific configuration settings for the
 * SIO Network adapter on the ICAR05 vehicle platform. It defines feature
 * enable/disable flags and adapter-specific parameters.
 *
 * <AUTHOR> Documentation
 * @date 2025-06-24
 * @version 1.0
 * @platform ICAR05
 */

#ifndef SIO_NET_ADAPTER_CFG_HPP
#define SIO_NET_ADAPTER_CFG_HPP

namespace sio_net {

/** @brief Feature enable flag value */
#define SIO_NET_ON              1

/** @brief Feature disable flag value */
#define SIO_NET_OFF             0

/** @brief TX signal array support configuration (disabled for ICAR05) */
#define SUPPORT_TX_SIGNAL_ARRAY SIO_NET_OFF

} // namespace sio_net

#endif