
project(SIO_NET_CFG LANGUAGES C CXX)

zx_dota_parse_component_model(COMPONENT_NAME ${PROJECT_NAME})
zx_component_pre_setup(COMPONENT_NAME ${PROJECT_NAME})

#=============================================================================
# DO NOT CHANGE BEFORE THESE LINES
#=============================================================================
add_library(
    ${PROJECT_NAME}
    ${ZX_DOTA_COMPONENT_${PROJECT_NAME}_SOURCES}
    ${ZX_DOTA_COMPONENT_${PROJECT_NAME}_HEADERS}
)
target_link_libraries(
    ${PROJECT_NAME}
    PRIVATE
        pubif
        prvif::pf
        prvif::microsar
        prvif::cpj
        prvif::bsw
)
target_include_directories(
    ${PROJECT_NAME}
    PUBLIC 
        ${CMAKE_CURRENT_SOURCE_DIR}/inc
        ${CMAKE_CURRENT_SOURCE_DIR}/src
)
#=============================================================================
set(DOXYGEN_EXECUTABLE_FILES 
    ${CMAKE_HOME_DIRECTORY}/components/SIO_NET/inc/MetaMacro.hpp
)

set(DOXYGEN_SOURCES
    ${CMAKE_HOME_DIRECTORY}/components/SIO_NET/src
    ${CMAKE_CURRENT_SOURCE_DIR}/../common/src
)

set(DOXYGEN_HEADERS
    ${CMAKE_HOME_DIRECTORY}/components/SIO_NET/inc
    ${CMAKE_CURRENT_SOURCE_DIR}/../common/inc
)

set_target_properties(${PROJECT_NAME} PROPERTIES
    HAS_DOCUMENTATION TRUE
    DOCUMENTATION_DIR "${CMAKE_CURRENT_SOURCE_DIR}/doc"
    DOCUMENTATION_SOURCES "${DOXYGEN_SOURCES}"
    DOCUMENTATION_HEADERS "${DOXYGEN_HEADERS}"
    DOCUMENTATION_EXECUTABLE_FILES "${DOXYGEN_EXECUTABLE_FILES}"
)

set_property(GLOBAL APPEND PROPERTY PROJECT_ALL_MODULES ${PROJECT_NAME})