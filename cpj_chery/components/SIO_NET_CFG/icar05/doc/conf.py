# -*- coding: utf-8 -*-
"""
SIO_NET Unit Design Document Configuration
==========================================

Configuration file for Sphinx documentation builder for SIO_NET unit design document.
"""

import os
import sys

# -- Project information -----------------------------------------------------

project = 'SIO_NET Unit Design Document'
copyright = '2025, SIO_NET Development Team'
author = 'SIO_NET Development Team'

# The short X.Y version
version = '1.0'
# The full version, including alpha/beta/rc tags
release = '1.0.0'

# -- General configuration ---------------------------------------------------

# Add any Sphinx extension module names here, as strings. They can be
# extensions coming with <PERSON>phinx (named 'sphinx.ext.*') or your custom
# ones.
extensions = [
    'sphinx.ext.autodoc',
    'sphinx.ext.doctest',
    'sphinx.ext.intersphinx',
    'sphinx.ext.todo',
    'sphinx.ext.coverage',
    'sphinx.ext.mathjax',
    'sphinx.ext.ifconfig',
    'sphinx.ext.viewcode',
    'sphinx.ext.githubpages',
    'breathe',
    'sphinx.ext.graphviz',
    'sphinxcontrib.plantuml',
]

# Breathe Configuration for Doxygen integration
breathe_projects = {
    "SIO_NET": "../../../../../build/cpj_chery_icar05/docs/doxygen/xml"
}
breathe_default_project = "SIO_NET"

# Breathe 跳转配置 - 设置 Doxygen HTML 文档的 URL
breathe_doxygen_config_options = {
    'GENERATE_HTML': 'YES',
    'HTML_OUTPUT': 'html',
    'GENERATE_XML': 'YES',
    'XML_OUTPUT': 'xml',
    'HTML_FILE_EXTENSION': '.html'
}

# 配置外部文档链接
intersphinx_mapping = {
    'doxygen': ('../../../../../build/cpj_chery_icar05/docs/doxygen/html/', None),
}

# 自定义 Breathe 设置以生成正确的链接
breathe_show_define_initializer = True
breathe_show_enumvalue_initializer = True

# 自定义函数处理 Doxygen 链接
def setup(app):
    """
    自定义 Sphinx 配置，添加 Doxygen 链接处理
    """
    # 添加自定义 CSS 样式
    app.add_css_file('custom.css')
    
    # 注册事件处理器
    app.connect('html-page-context', add_doxygen_links)

def add_doxygen_links(app, pagename, templatename, context, doctree):
    """
    为页面添加 Doxygen 链接
    """
    # Doxygen HTML 文档的相对路径
    doxygen_base_url = '../../../../../build/cpj_chery_icar05/docs/doxygen/html/'
    context['doxygen_base_url'] = doxygen_base_url

# PlantUML configuration
plantuml = 'plantuml'
plantuml_output_format = 'svg'

# Add any paths that contain templates here, relative to this directory.
templates_path = ['_templates']

# The suffix(es) of source filenames.
source_suffix = '.rst'

# The master toctree document.
master_doc = 'index'

# The language for content autogenerated by Sphinx.
language = 'zh_CN'

# List of patterns, relative to source directory, that match files and
# directories to ignore when looking for source files.
exclude_patterns = ['_build', 'Thumbs.db', '.DS_Store']

# The name of the Pygments (syntax highlighting) style to use.
pygments_style = 'sphinx'

# -- Options for HTML output -------------------------------------------------

# The theme to use for HTML and HTML Help pages.
html_theme = 'sphinx_rtd_theme'

# Theme options are theme-specific and customize the look and feel of a theme
html_theme_options = {
    'canonical_url': '',
    'analytics_id': '',
    'logo_only': False,
    'display_version': True,
    'prev_next_buttons_location': 'bottom',
    'style_external_links': False,
    'vcs_pageview_mode': '',
    'style_nav_header_background': '#2980B9',
    # Toc options
    'collapse_navigation': True,
    'sticky_navigation': True,
    'navigation_depth': 4,
    'includehidden': True,
    'titles_only': False
}

# Add any paths that contain custom static files (such as style sheets) here,
# relative to this directory. They are copied after the builtin static files,
# so a file named "default.css" will overwrite the builtin "default.css".
html_static_path = ['_static']

# Custom sidebar templates, must be a dictionary that maps document names
# to template names.
html_sidebars = {}

# -- Options for HTMLHelp output ---------------------------------------------

# Output file base name for HTML help builder.
htmlhelp_basename = 'SIONETUnitDesignDoc'

# -- Options for LaTeX output ------------------------------------------------

latex_elements = {
    'papersize': 'a4paper',
    'pointsize': '10pt',
    'preamble': r'''
\usepackage{xeCJK}
\setCJKmainfont{SimSun}
\setCJKsansfont{SimHei}
\setCJKmonofont{FangSong}
''',
}

# Grouping the document tree into LaTeX files. List of tuples
# (source start file, target name, title,
#  author, documentclass [howto, manual, or own class]).
latex_documents = [
    (master_doc, 'SIONETUnitDesign.tex', 'SIO_NET Unit Design Document',
     'SIO_NET Development Team', 'manual'),
]

# -- Options for manual page output ------------------------------------------

# One entry per manual page. List of tuples
# (source start file, name, description, authors, manual section).
man_pages = [
    (master_doc, 'sionetunitdesign', 'SIO_NET Unit Design Document',
     [author], 1)
]

# -- Options for Texinfo output ----------------------------------------------

# Grouping the document tree into Texinfo files. List of tuples
# (source start file, target name, title, author,
#  dir menu entry, description, category)
texinfo_documents = [
    (master_doc, 'SIONETUnitDesign', 'SIO_NET Unit Design Document',
     author, 'SIONETUnitDesign', 'Unit design document for SIO_NET component.',
     'Miscellaneous'),
]

# -- Extension configuration -------------------------------------------------

# -- Options for intersphinx extension ---------------------------------------

# Example configuration for intersphinx: refer to the Python standard library.
intersphinx_mapping = {'https://docs.python.org/': None}

# -- Options for todo extension ----------------------------------------------

# If true, `todo` and `todoList` produce output, else they produce nothing.
todo_include_todos = True

# -- Custom configuration ----------------------------------------------------

# Number figures, tables and code-blocks automatically
numfig = True
numfig_format = {
    'figure': 'Figure %s',
    'table': 'Table %s',
    'code-block': 'Listing %s',
    'section': 'Section %s',
}
