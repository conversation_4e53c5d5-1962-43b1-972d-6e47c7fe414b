============================
5. 组件接口设计
============================

本章节描述SIO_NET组件的接口设计，包括基本类型定义、数据结构定义、常量与变量等。
接口文档通过Doxygen自动生成，确保与代码实现保持同步。

5.1 基本类型定义 (Basic Type Definitions)
=========================================

5.1.1 基本数据类型 (Primitive Types)
------------------------------------

SIO_NET组件使用标准的AUTOSAR基本数据类型：

.. code-block:: c++

   // 基本整数类型
   typedef unsigned char      UB;    // 无符号字节 (0-255)
   typedef unsigned short     UW;    // 无符号字 (0-65535)
   typedef unsigned long      UL;    // 无符号长字
   typedef signed char        SB;    // 有符号字节
   typedef signed short       SW;    // 有符号字
   typedef signed long        SL;    // 有符号长字
   
   // 布尔类型
   typedef unsigned char      B;     // 布尔类型 (TRUE/FALSE)
   typedef unsigned char      BOOL;  // 布尔类型 (TRUE/FALSE)
   
   // 浮点类型
   typedef float              F32;   // 32位浮点数
   typedef double             F64;   // 64位浮点数

5.1.2 自定义类型 (Custom Types)
-------------------------------

.. doxygenfile:: Net_SignalRefl_Type.hpp
   :project: SIO_NET_CFG

5.1.3 枚举类型 (Enumeration Types)
----------------------------------

**网络信号格式返回值**

.. doxygenenum:: sio_net::NetSignalFormatRet_En
   :project: SIO_NET_CFG

**无效值类型**

.. doxygenenum:: sio_net::InvalidValueType
   :project: SIO_NET_CFG

**网络消息状态**

.. doxygenenum:: sio_net::NetMsgSatus_En
   :project: SIO_NET_CFG

5.2 数据结构定义 (Data Structure Definitions)
=============================================

5.2.1 结构体 (Structures)
-------------------------

**信号格式参数结构体**

.. code-block:: c++

   typedef struct {
       vfc::float32_t factor;        // 缩放因子
       vfc::float32_t offset;        // 偏移量
       vfc::float32_t unit_factor;   // 单位转换因子
       vfc::uint32_t  norming_factor; // 归一化因子
       vfc::uint32_t  can_handle_id;  // CAN句柄ID
       vfc::uint32_t  inval_dem_id;   // 无效值DEM事件ID
       bool (*inval_check_fun)(const vfc::uint32_t*); // 无效值检查函数
       const SignalInvalidValueTable_ST* inval_val_table; // 无效值表
   } SignalFormat_ST;

**CAN段E2E信息结构体**

.. code-block:: c++

   struct CANSegment_E2E_Info {
       const UW data_id;       // 数据标识符
       const UW seg_offset;    // 段偏移量
       const UW cnt_bit_start; // 计数器起始位
       const UW crc_offset;    // CRC偏移量
   };

**CAN消息E2E配置结构体**

.. doxygenstruct:: CANMsg_E2E_Cfg
   :project: SIO_NET_CFG
   :members:

:doxystruct:`_c_a_n_msg___e2_e___cfg`

**CAN接收段E2E信息结构体**

.. doxygenstruct:: CANRxSegment_E2E_Info
   :project: SIO_NET_CFG
   :members:

:doxystruct:`_c_a_n_rx_segment___e2_e___info`

**CAN接收消息E2E配置结构体**

.. doxygenstruct:: CANRxMsg_E2E_Cfg
   :project: SIO_NET_CFG
   :members:
   :undoc-members:

:doxystruct:`_c_a_n_rx_msg___e2_e___cfg`

**信号无效值表结构体**

.. doxygenstruct:: sio_net::SignalInvalidValueTable_ST
   :project: SIO_NET_CFG
   :members:

:doxystruct:`sio__net_1_1_signal_invalid_value_table___s_t`

**多值类型结构体**

.. doxygenstruct:: sio_net::MultiValueType
   :project: SIO_NET_CFG
   :members:

:doxystruct:`sio__net_1_1_multi_value_type`

5.2.2 联合体 (Unions)
--------------------

SIO_NET组件中暂无联合体定义。

5.2.3 类 (Classes)
------------------

**网络模式管理类**

.. doxygenclass:: sio_net::NetMode
   :project: SIO_NET_CFG
   :members:

:doxyclass:`sio__net_1_1_net_mode`

**总线关闭模式管理类**

.. doxygenclass:: sio_net::BusoffMode
   :project: SIO_NET_CFG
   :members:

:doxyclass:`sio__net_1_1_busoff_mode`

**网络接收运行基类**

.. doxygenclass:: sio_net::NET_RX_RunnableBase
   :project: SIO_NET_CFG
   :members:

:doxyclass:`sio__net_1_1_n_e_t___r_x___runnable_base`

**网络发送运行基类**

.. doxygenclass:: sio_net::NET_TX_RunnableBase
   :project: SIO_NET_CFG
   :members:

:doxyclass:`sio__net_1_1_n_e_t___t_x___runnable_base`

**CAN消息模板类**

.. doxygenclass:: sio_net::CanMsgTemp
   :project: SIO_NET_CFG
   :members:

:doxyclass:`sio__net_1_1_can_msg_temp`

5.2.4 接口 (Interfaces)
-----------------------

SIO_NET组件主要通过C接口函数提供服务，详见函数详细设计章节。

5.3 常量与变量 (Constants and Variables)
=======================================

5.3.1 常量定义 (Constants)
--------------------------

**网络配置常量**

.. doxygendefine:: NETCFG_CAN_PUB_USED
   :project: SIO_NET_CFG

.. doxygendefine:: NETCFG_CAN_PR1_USED
   :project: SIO_NET_CFG

.. doxygendefine:: NETCFG_CAN_PR2_USED
   :project: SIO_NET_CFG

.. doxygendefine:: NETCFG_CAN_PR3_USED
   :project: SIO_NET_CFG

**诊断时间常量**

.. doxygendefine:: SIO_NET_DIAGNOSTIC_INITIAL_TIME
   :project: SIO_NET_CFG

.. doxygendefine:: SIO_NET_DIAGNOSTIC_START_TIME
   :project: SIO_NET_CFG

.. doxygendefine:: SIO_NET_DIAGNOSTIC_RESTART_TIME
   :project: SIO_NET_CFG

**总线关闭相关常量**

.. doxygendefine:: BUSOFF_EC_RECOVER_DELAY_TIME
   :project: SIO_NET_CFG

.. doxygendefine:: DemEC_BusOff_Invalid
   :project: SIO_NET_CFG

**E2E保护常量**

.. doxygenvariable:: CAN_E2E_CNT_MAX
   :project: SIO_NET_CFG

.. doxygenvariable:: CAN_E2E_CNT_MIN
   :project: SIO_NET_CFG

.. doxygenvariable:: CAN_E2E_INDEX_NULL
   :project: SIO_NET_CFG

.. doxygenvariable:: CAN_E2E_CNT_ERROR
   :project: SIO_NET_CFG

.. doxygenvariable:: CAN_E2E_CNT_DEFAULT
   :project: SIO_NET_CFG

**信号处理常量**

.. doxygenvariable:: SIO_NET_SIG_INVAL_EVENT_ID_DEFAULT
   :project: SIO_NET_CFG

.. doxygendefine:: DEFAULT_EVENT
   :project: SIO_NET_CFG

5.3.2 全局变量 (Global Variables)
---------------------------------

**信号参数数组**

SIO_NET组件使用全局数组存储信号格式参数：

.. code-block:: c++

   // 接收信号参数数组
   extern SignalFormat_ST m_rx_signal_parameter[];

   // 发送信号参数数组
   extern SignalFormat_ST m_tx_signal_parameter[];

5.3.3 静态变量 (Static Variables)
---------------------------------

静态变量为模块内部使用，不对外暴露接口。主要包括：

* 运行列表管理变量
* 内部状态变量
* 缓存和临时变量

5.3.4 宏定义 (Macro Definitions)
--------------------------------

**功能开关宏**

.. doxygendefine:: SIO_NET_ON
   :project: SIO_NET_CFG

.. doxygendefine:: SIO_NET_OFF
   :project: SIO_NET_CFG

.. doxygendefine:: SUPPORT_TX_SIGNAL_ARRAY
   :project: SIO_NET_CFG

**信号格式化宏**

.. doxygendefine:: GET_SIGNAL_INVAL_VALUE_TABLE
   :project: SIO_NET_CFG

.. doxygendefine:: GET_SIGNAL_INVAL_VALUE_TABLE_PTR
   :project: SIO_NET_CFG

.. doxygendefine:: CREAT_INVAL_LIST
   :project: SIO_NET_CFG

.. doxygendefine:: CREAT_INVAL_TABLE
   :project: SIO_NET_CFG

.. doxygendefine:: CREAT_INVAL_RANGE
   :project: SIO_NET_CFG

.. doxygendefine:: FIELD_SIGNAL_FORMAT_PARAMETER
   :project: SIO_NET_CFG

**工具宏**

.. doxygendefine:: WEAK_FUN
   :project: SIO_NET_CFG

.. note::
   本章节的接口定义通过Doxygen自动生成，确保与源代码保持同步。
   
   **访问完整API文档：**
   
   - 点击上述的蓝色链接（如 :doxystruct:`_c_a_n_rx_msg___e2_e___cfg`）可直接跳转到对应的Doxygen详细文档
   - 或直接访问完整的 `Doxygen API文档 <../../doxygen/html/index.html>`_

.. warning::
   接口的使用必须严格按照文档说明进行，不当使用可能导致系统异常或安全问题。
