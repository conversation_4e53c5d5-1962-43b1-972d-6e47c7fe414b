==========================
4. 组件架构设计
==========================

4.1 静态架构 (Static Architecture)
==================================

4.1.1 组件层次结构
------------------

SIO_NET组件采用分层架构设计，从上到下分为接口层、控制层、处理层和配置层。

.. uml::
   :caption: SIO_NET组件静态架构图

   package "SIO_NET Component" {
       package "Interface Layer" {
           [SIO_Net_Controller] as Controller
           [C Interface Functions] as CInterface
       }
       
       package "Control Layer" {
           [NetMode] as NetMode
           [BusoffMode] as BusoffMode
           [RunnableList] as RunnableList
       }
       
       package "Processing Layer" {
           [Message Processing] as MsgProc
           [Signal Processing] as SigProc
           [E2E Protection] as E2E
           [ComCallout] as Callout
       }
       
       package "Configuration Layer" {
           [Net_Cfg] as NetCfg
           [Message Instances] as MsgInst
           [Signal Adapters] as SigAdapt
           [Platform Config] as PlatCfg
       }
   }
   
   package "External Dependencies" {
       [COM Module] as COM
       [DEM Module] as DEM
       [ComM Module] as ComM
       [RTE] as RTE
   }
   
   Controller --> NetMode
   Controller --> RunnableList
   NetMode --> BusoffMode
   RunnableList --> MsgProc
   MsgProc --> SigProc
   MsgProc --> E2E
   SigProc --> Callout
   
   Controller --> CInterface
   MsgProc --> MsgInst
   SigProc --> SigAdapt
   NetMode --> NetCfg
   BusoffMode --> PlatCfg
   
   Callout --> COM
   NetMode --> DEM
   NetMode --> ComM
   Controller --> RTE

4.1.2 核心类关系
----------------

.. uml::
   :caption: SIO_NET核心类关系图

   class NetMode {
       -m_netDiagStartDelayCnt_uw : UW
       -m_netDiagStartRequest_b : B
       -m_netRunRequest_b : B
       -ins_pubBusoffMode : BusoffMode
       -ins_pr1BusoffMode : BusoffMode
       +e_Init_V() : void
       +e_Run_V() : void
       +e_EnableNetCom_V() : void
       +e_DisableNetCom_V() : void
   }
   
   class BusoffMode {
       -m_canChannel_ub : UB
       -m_demECBusoffIndex_ub : UB
       -m_lastBusoffState_ub : UB
       -m_busoffECRecoverDelayCnt_uw : UW
       +e_Init_V(channel: UB, ecIndex: UB) : void
       +e_Run_V() : void
   }
   
   class "NET_RX_RunnableBase" {
       +{static} init_list() : void
       +{static} run_list() : void
       +{abstract} run() : void
   }
   
   class "NET_TX_RunnableBase" {
       +{static} init_list() : void
       +{static} run_list() : void
       +{abstract} run() : void
   }
   
   class "CanMsgTemp<T>" {
       +signal_receive(msg: T&) : void
       +signal_send(msg: T&) : void
   }
   
   NetMode *-- BusoffMode : contains
   "NET_RX_RunnableBase" <|-- "CanMsgTemp<T>"
   "NET_TX_RunnableBase" <|-- "CanMsgTemp<T>"

4.2 动态行为 (Dynamic Behavior)
===============================

4.2.1 系统初始化流程
--------------------

.. uml::
   :caption: SIO_NET初始化时序图

   participant "System" as Sys
   participant "SIO_Net_Controller" as Ctrl
   participant "NetMode" as Mode
   participant "BusoffMode" as Busoff
   participant "RunnableList" as List
   
   Sys -> Ctrl : Net_MsgRxInit()
   activate Ctrl
   
   Ctrl -> List : m_NetRunnableInit()
   activate List
   List -> List : NET_TX_RunnableBase::init_list()
   List -> List : NET_RX_RunnableBase::init_list()
   deactivate List
   
   Ctrl -> Mode : e_Init_V()
   activate Mode
   Mode -> Busoff : e_Init_V(channel, ecIndex)
   activate Busoff
   Busoff -> Busoff : Initialize channel parameters
   deactivate Busoff
   deactivate Mode
   
   deactivate Ctrl

4.2.2 消息处理流程
------------------

.. uml::
   :caption: 消息处理时序图

   participant "Scheduler" as Sched
   participant "SIO_Net_Controller" as Ctrl
   participant "RunnableList" as List
   participant "TX_Runnable" as TX
   participant "RX_Runnable" as RX
   participant "COM" as COM
   
   Sched -> Ctrl : Net_MsgRx_T10() [10ms cycle]
   activate Ctrl
   
   Ctrl -> List : m_NetRunnableRun()
   activate List
   
   List -> TX : run_list()
   activate TX
   loop for each TX message
       TX -> TX : signal_send()
       TX -> COM : Com_SendSignal()
   end
   deactivate TX
   
   List -> RX : run_list()
   activate RX
   loop for each RX message
       RX -> COM : Com_ReceiveSignal()
       RX -> RX : signal_receive()
       RX -> RX : format_sig()
   end
   deactivate RX
   
   deactivate List
   deactivate Ctrl

4.3 状态图 (State Diagrams)
===========================

4.3.1 网络模式状态图
--------------------

.. uml::
   :caption: NetMode状态转换图

   [*] --> Uninitialized
   
   Uninitialized --> Initialized : e_Init_V()
   
   state Initialized {
       [*] --> ComDisabled
       
       ComDisabled --> ComEnabled : e_EnableNetCom_V()
       ComEnabled --> ComDisabled : e_DisableNetCom_V()
       
       state ComEnabled {
           [*] --> DiagDisabled
           DiagDisabled --> DiagEnabled : e_EnableNetDiag_V()
           DiagEnabled --> DiagDisabled : e_DisableNetDiag_V()
           
           state DiagEnabled {
               [*] --> DiagDelay
               DiagDelay --> DiagActive : delay_timeout
               DiagActive --> DiagDelay : e_SetDiagRestartTime()
           }
       }
   }

4.3.2 总线关闭模式状态图
------------------------

.. uml::
   :caption: BusoffMode状态转换图

   [*] --> Normal
   
   Normal --> BusoffDetected : CAN_busoff_detected
   BusoffDetected --> RecoveryDelay : start_recovery_timer
   RecoveryDelay --> Normal : recovery_timeout
   RecoveryDelay --> BusoffDetected : CAN_busoff_detected_again
   
   state RecoveryDelay {
       state "Counting" as Counting
       [*] --> Counting
       Counting : entry / m_busoffECRecoverDelayCnt_uw--
       Counting --> [*] : [counter == 0]
   }

4.4 时序图 (Sequence Diagrams)
==============================

4.4.1 E2E保护处理时序
---------------------

.. uml::
   :caption: E2E保护处理时序图

   participant "Message Handler" as Handler
   participant "E2E Protection" as E2E
   participant "CRC Calculator" as CRC
   participant "Counter Manager" as Counter
   participant "DEM" as DEM
   
   Handler -> E2E : Process received message
   activate E2E
   
   E2E -> Counter : Check rolling counter
   activate Counter
   Counter -> Counter : Validate counter sequence
   alt Counter valid
       Counter --> E2E : Counter OK
   else Counter invalid
       Counter -> DEM : Report counter error
       Counter --> E2E : Counter Error
   end
   deactivate Counter
   
   E2E -> CRC : Calculate CRC
   activate CRC
   CRC -> CRC : Compute CRC value
   CRC --> E2E : CRC result
   deactivate CRC
   
   E2E -> E2E : Compare calculated vs received CRC
   alt CRC valid
       E2E -> DEM : Report CRC passed
       E2E --> Handler : Message valid
   else CRC invalid
       E2E -> DEM : Report CRC error
       E2E --> Handler : Message invalid
   end
   
   deactivate E2E

4.4.2 信号格式化处理时序
------------------------

.. uml::
   :caption: 信号格式化时序图

   participant "Signal Processor" as Proc
   participant "Format Function" as Format
   participant "Invalid Checker" as Checker
   participant "DEM" as DEM
   participant "Application" as App
   
   Proc -> Format : m_NetRxSignalFormat()
   activate Format
   
   Format -> Format : Apply scaling factor
   Format -> Format : Apply offset
   Format -> Format : Apply unit conversion
   Format -> Format : Apply normalization
   Format --> Proc : Formatted value
   deactivate Format
   
   Proc -> Checker : m_NetRxSignalInvalCheck()
   activate Checker
   
   Checker -> Checker : Check invalid value table
   alt Value valid
       Checker -> DEM : Report signal passed
   else Value invalid
       Checker -> DEM : Report signal failed
   end
   deactivate Checker
   
   Proc -> App : Provide formatted signal
