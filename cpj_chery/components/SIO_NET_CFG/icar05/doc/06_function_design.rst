==========================
6. 函数详细设计
==========================

本章节描述SIO_NET组件的函数详细设计，包括函数列表、函数详述、调用关系和算法描述。
函数文档通过Doxygen自动生成，确保与代码实现保持同步。

6.1 函数列表 (Function List)
============================

6.1.1 主要接口函数
------------------

.. list-table:: 主要接口函数列表
   :widths: 30 20 50
   :header-rows: 1

   * - 函数名
     - 类型
     - 功能描述
   * - Net_MsgRxInit
     - C接口
     - 初始化网络消息接收系统
   * - Net_MsgRx_T10
     - C接口
     - 执行网络消息处理(10ms周期)
   * - net_mode_req_com_start_v
     - C接口
     - 请求启动网络通信
   * - net_mode_req_com_stop_v
     - C接口
     - 请求停止网络通信
   * - net_mode_req_enable_net_monitor_v
     - C接口
     - 启用网络监控
   * - net_mode_req_disable_net_monitor_v
     - C接口
     - 禁用网络监控

6.1.2 类成员函数
----------------

.. list-table:: NetMode类成员函数
   :widths: 30 50
   :header-rows: 1

   * - 函数名
     - 功能描述
   * - e_Init_V
     - 初始化网络模式管理
   * - e_Run_V
     - 执行网络模式管理
   * - e_EnableNetCom_V
     - 启用网络通信
   * - e_DisableNetCom_V
     - 禁用网络通信
   * - e_EnableNetDiag_V
     - 启用网络诊断
   * - e_DisableNetDiag_V
     - 禁用网络诊断

.. list-table:: BusoffMode类成员函数
   :widths: 30 50
   :header-rows: 1

   * - 函数名
     - 功能描述
   * - e_Init_V
     - 初始化总线关闭模式管理
   * - e_Run_V
     - 执行总线关闭模式管理

6.1.3 模板函数
--------------

.. list-table:: 模板函数列表
   :widths: 30 50
   :header-rows: 1

   * - 函数名
     - 功能描述
   * - m_NetRxSignalFormat
     - 格式化接收信号值
   * - _IS_MsgLenValid
     - 验证消息长度有效性
   * - _UpdateCountValue
     - 更新计数器值
   * - format_sig
     - 格式化单个信号

6.2 函数详述 (Function Details)
===============================

6.2.1 主要接口函数详述
----------------------

**网络消息接收初始化函数**

.. doxygenfunction:: Net_MsgRxInit
   :project: SIO_NET_CFG

**网络消息处理函数**

.. doxygenfunction:: Net_MsgRx_T10
   :project: SIO_NET_CFG

**网络通信控制函数**

.. doxygenfunction:: net_mode_req_com_start_v
   :project: SIO_NET_CFG

.. doxygenfunction:: net_mode_req_com_stop_v
   :project: SIO_NET_CFG

**网络监控控制函数**

.. doxygenfunction:: net_mode_req_enable_net_monitor_v
   :project: SIO_NET_CFG

.. doxygenfunction:: net_mode_req_disable_net_monitor_v
   :project: SIO_NET_CFG

**诊断时间设置函数**

.. doxygenfunction:: net_mode_set_diag_restart_time_v
   :project: SIO_NET_CFG

.. doxygenfunction:: net_mode_set_diag_start_time_v
   :project: SIO_NET_CFG

**运行请求控制函数**

.. doxygenfunction:: net_mode_set_run_request_flag_v
   :project: SIO_NET_CFG

.. doxygenfunction:: net_mode_get_run_request_flag_B
   :project: SIO_NET_CFG

6.2.2 类成员函数详述
--------------------

**NetMode类函数**

.. doxygenfunction:: sio_net::NetMode::e_Init_V
   :project: SIO_NET_CFG

.. doxygenfunction:: sio_net::NetMode::e_Run_V
   :project: SIO_NET_CFG

.. doxygenfunction:: sio_net::NetMode::e_EnableNetCom_V
   :project: SIO_NET_CFG

.. doxygenfunction:: sio_net::NetMode::e_DisableNetCom_V
   :project: SIO_NET_CFG

.. doxygenfunction:: sio_net::NetMode::e_EnableNetDiag_V
   :project: SIO_NET_CFG

.. doxygenfunction:: sio_net::NetMode::e_DisableNetDiag_V
   :project: SIO_NET_CFG

.. doxygenfunction:: sio_net::NetMode::e_SetDiagRestartTime
   :project: SIO_NET_CFG

.. doxygenfunction:: sio_net::NetMode::e_SetDiagStartTime
   :project: SIO_NET_CFG

.. doxygenfunction:: sio_net::NetMode::e_SetNetRunRequest_V
   :project: SIO_NET_CFG

.. doxygenfunction:: sio_net::NetMode::e_GetNetRunRequest_B
   :project: SIO_NET_CFG

**BusoffMode类函数**

.. doxygenfunction:: sio_net::BusoffMode::e_Init_V
   :project: SIO_NET_CFG

.. doxygenfunction:: sio_net::BusoffMode::e_Run_V
   :project: SIO_NET_CFG

6.2.3 模板函数详述
------------------

**信号格式化函数**

.. doxygenfunction:: sio_net::m_NetRxSignalFormat
   :project: SIO_NET_CFG

**消息长度验证函数**

.. doxygenfunction:: _IS_MsgLenValid
   :project: SIO_NET_CFG

**计数器更新函数**

.. doxygenfunction:: _UpdateCountValue
   :project: SIO_NET_CFG

.. doxygenfunction:: _UpdateSigCountValue
   :project: SIO_NET_CFG

**信号处理函数**

.. doxygenfunction:: sio_net::format_sig
   :project: SIO_NET_CFG

.. doxygenfunction:: sio_net::receive_signal
   :project: SIO_NET_CFG

6.2.4 E2E保护函数详述
---------------------

**计数器设置函数**

.. doxygenfunction:: _SetCntFormMsg
   :project: SIO_NET_CFG

**无效值检查函数**

.. doxygenfunction:: sio_net::m_NetRxSignalInvalCheck
   :project: SIO_NET_CFG

.. doxygenfunction:: sio_net::m_NetCheckInvalidValueTable
   :project: SIO_NET_CFG

6.3 调用关系 (Call Relationships)
=================================

6.3.1 函数调用层次图
--------------------

.. uml::
   :caption: 主要函数调用关系图

   @startuml
   
   package "C Interface Layer" {
       [Net_MsgRxInit] as Init
       [Net_MsgRx_T10] as Run
       [net_mode_req_com_start_v] as ComStart
   }
   
   package "Control Layer" {
       [m_NetRunnableInit] as RInit
       [m_NetRunnableRun] as RRun
       [NetMode::e_EnableNetCom_V] as EnableCom
   }
   
   package "Processing Layer" {
       [NET_TX_RunnableBase::init_list] as TXInit
       [NET_RX_RunnableBase::init_list] as RXInit
       [NET_TX_RunnableBase::run_list] as TXRun
       [NET_RX_RunnableBase::run_list] as RXRun
   }
   
   package "Signal Processing" {
       [format_sig] as FormatSig
       [m_NetRxSignalFormat] as SignalFormat
       [m_NetRxSignalInvalCheck] as InvalCheck
   }
   
   Init --> RInit
   RInit --> TXInit
   RInit --> RXInit
   
   Run --> RRun
   RRun --> TXRun
   RRun --> RXRun
   
   ComStart --> EnableCom
   
   RXRun --> FormatSig
   FormatSig --> SignalFormat
   FormatSig --> InvalCheck
   
   @enduml

6.3.2 函数调用时序
------------------

.. uml::
   :caption: 函数调用时序图

   @startuml
   
   participant "System" as Sys
   participant "Net_MsgRxInit" as Init
   participant "m_NetRunnableInit" as RInit
   participant "TX_Init" as TXInit
   participant "RX_Init" as RXInit
   
   == 初始化阶段 ==
   Sys -> Init : 系统启动时调用
   activate Init
   Init -> RInit : 调用内部初始化
   activate RInit
   RInit -> TXInit : 初始化发送列表
   activate TXInit
   TXInit --> RInit : 完成
   deactivate TXInit
   RInit -> RXInit : 初始化接收列表
   activate RXInit
   RXInit --> RInit : 完成
   deactivate RXInit
   RInit --> Init : 完成
   deactivate RInit
   Init --> Sys : 完成
   deactivate Init
   
   @enduml

.. uml::
   :caption: 运行时调用时序图

   @startuml
   
   participant "Timer" as Timer
   participant "Net_MsgRx_T10" as Run
   participant "m_NetRunnableRun" as RRun
   participant "TX_Run" as TXRun
   participant "RX_Run" as RXRun
   participant "format_sig" as Format
   
   == 运行阶段 (10ms周期) ==
   Timer -> Run : 10ms周期触发
   activate Run
   Run -> RRun : 调用运行处理
   activate RRun
   
   par 并行处理
       RRun -> TXRun : 执行发送处理
       activate TXRun
       TXRun --> RRun : 完成
       deactivate TXRun
   else
       RRun -> RXRun : 执行接收处理
       activate RXRun
       RXRun -> Format : 格式化信号
       activate Format
       Format --> RXRun : 完成
       deactivate Format
       RXRun --> RRun : 完成
       deactivate RXRun
   end
   
   RRun --> Run : 完成
   deactivate RRun
   Run --> Timer : 完成
   deactivate Run
   
   @enduml

**初始化阶段调用时序**

1. ``Net_MsgRxInit()`` - 系统启动时调用
2. ``m_NetRunnableInit()`` - 内部初始化
3. ``NET_TX_RunnableBase::init_list()`` - 发送列表初始化
4. ``NET_RX_RunnableBase::init_list()`` - 接收列表初始化

**运行阶段调用时序**

1. ``Net_MsgRx_T10()`` - 10ms周期调用
2. ``m_NetRunnableRun()`` - 内部运行处理
3. ``NET_TX_RunnableBase::run_list()`` - 发送处理（并行）
4. ``NET_RX_RunnableBase::run_list()`` - 接收处理（并行）
5. ``format_sig()`` - 信号格式化
6. ``m_NetRxSignalFormat()`` - 信号值转换
7. ``m_NetRxSignalInvalCheck()`` - 无效值检查

.. note::
   发送处理和接收处理可以并行执行，提高系统性能。
   信号格式化在接收处理过程中按需调用。

6.4 算法描述 (Algorithm Description)
===================================

6.4.1 信号格式化算法
--------------------

信号格式化算法将原始CAN信号值转换为应用层使用的物理值：

.. code-block:: text

   算法：信号格式化
   输入：raw_value (原始值), parameter (格式参数)
   输出：formatted_value (格式化值)
   
   BEGIN
       temp_value = (float)raw_value
       
       IF parameter.factor > epsilon THEN
           temp_value = temp_value * parameter.factor
       END IF
       
       temp_value = temp_value + parameter.offset
       
       IF parameter.unit_factor > epsilon THEN
           temp_value = temp_value * parameter.unit_factor
       END IF
       
       IF target_type is floating_point THEN
           formatted_value = (target_type)temp_value
       ELSE
           IF parameter.norming_factor != 0 THEN
               formatted_value = Normalize(temp_value, parameter.norming_factor)
           ELSE
               formatted_value = (target_type)temp_value
           END IF
       END IF
   END

6.4.2 E2E计数器验证算法
-----------------------

E2E计数器验证算法确保消息的时序正确性：

.. code-block:: text

   算法：计数器验证
   输入：received_counter, expected_counter
   输出：validation_result
   
   BEGIN
       IF received_counter == expected_counter THEN
           validation_result = VALID
           expected_counter = (expected_counter + 1) % MAX_COUNTER
       ELSE IF received_counter == (expected_counter - 1) % MAX_COUNTER THEN
           validation_result = REPEATED
       ELSE
           validation_result = INVALID
           expected_counter = (received_counter + 1) % MAX_COUNTER
       END IF
   END

6.4.3 CRC校验算法
-----------------

CRC校验算法验证数据完整性：

.. code-block:: text

   算法：CRC校验
   输入：message_data, crc_config
   输出：crc_valid
   
   BEGIN
       calculated_crc = 0
       
       FOR each byte in message_data EXCEPT crc_field DO
           calculated_crc = CRC_Calculate(calculated_crc, byte, crc_config)
       END FOR
       
       received_crc = Extract_CRC_From_Message(message_data)
       
       IF calculated_crc == received_crc THEN
           crc_valid = TRUE
       ELSE
           crc_valid = FALSE
       END IF
   END

.. note::
   本章节的函数文档通过Doxygen自动生成，确保与源代码保持同步。
   详细的函数实现请参考源代码文件。

.. warning::
   函数的调用必须遵循正确的时序和参数要求，错误的调用可能导致系统异常。
