===============================
2. 定义与缩略语
===============================

2.1 术语定义 (Terms Definition)
===============================

.. glossary::

   CAN总线 (Controller Area Network)
      一种串行通信协议，广泛应用于汽车电子系统中，用于连接各种电子控制单元(ECU)。

   E2E保护 (End-to-End Protection)
      端到端数据保护机制，通过CRC校验、滚动计数器等方式确保数据传输的完整性和正确性。

   信号 (Signal)
      CAN消息中的数据单元，代表特定的物理量或状态信息，如车速、转向角等。

   消息 (Message)
      CAN网络上传输的数据帧，包含一个或多个信号，具有唯一的标识符(ID)。

   PDU (Protocol Data Unit)
      协议数据单元，在网络通信中传输的数据包格式。

   DEM (Diagnostic Event Manager)
      诊断事件管理器，负责收集、存储和报告系统中的故障和诊断信息。

   COM (Communication)
      通信模块，负责处理网络消息的收发和信号的打包解包。

   AUTOSAR (AUTomotive Open System ARchitecture)
      汽车开放系统架构，为汽车电子软件开发提供标准化平台。

   ECU (Electronic Control Unit)
      电子控制单元，汽车中的嵌入式计算机系统。

   BSW (Basic Software)
      基础软件，AUTOSAR架构中的底层软件模块。

   RTE (Runtime Environment)
      运行时环境，AUTOSAR架构中连接应用软件和基础软件的中间层。

   SWC (Software Component)
      软件组件，AUTOSAR架构中的应用软件单元。

2.2 缩略语定义 (Abbreviations)
==============================

.. list-table:: 缩略语对照表
   :widths: 15 35 50
   :header-rows: 1

   * - 缩略语
     - 英文全称
     - 中文含义
   * - API
     - Application Programming Interface
     - 应用程序编程接口
   * - BSW
     - Basic Software
     - 基础软件
   * - CAN
     - Controller Area Network
     - 控制器局域网
   * - COM
     - Communication
     - 通信模块
   * - CRC
     - Cyclic Redundancy Check
     - 循环冗余校验
   * - DEM
     - Diagnostic Event Manager
     - 诊断事件管理器
   * - DLC
     - Data Length Code
     - 数据长度码
   * - E2E
     - End-to-End
     - 端到端
   * - ECU
     - Electronic Control Unit
     - 电子控制单元
   * - ID
     - Identifier
     - 标识符
   * - PDU
     - Protocol Data Unit
     - 协议数据单元
   * - RTE
     - Runtime Environment
     - 运行时环境
   * - SWC
     - Software Component
     - 软件组件
   * - TX
     - Transmit
     - 发送
   * - RX
     - Receive
     - 接收
   * - CFG
     - Configuration
     - 配置
   * - NET
     - Network
     - 网络
   * - SIO
     - Signal Input/Output
     - 信号输入输出
   * - PUB
     - Public
     - 公共
   * - PR1/PR2/PR3
     - Private 1/2/3
     - 私有1/2/3
   * - BMS
     - Battery Management System
     - 电池管理系统
   * - EPS
     - Electric Power Steering
     - 电动助力转向
   * - VCU
     - Vehicle Control Unit
     - 整车控制器
   * - ICM
     - Instrument Cluster Module
     - 仪表模块

2.3 命名约定 (Naming Conventions)
=================================

2.3.1 文件命名约定
------------------

.. list-table:: 文件命名规则
   :widths: 20 30 50
   :header-rows: 1

   * - 文件类型
     - 命名格式
     - 示例
   * - 头文件
     - ModuleName.h / ModuleName.hpp
     - SIO_Net_Controller.hpp
   * - 源文件
     - ModuleName.c / ModuleName.cpp
     - SIO_Net_Controller.cpp
   * - 配置文件
     - ModuleName_Cfg.h
     - Net_Cfg.h
   * - 类型定义文件
     - ModuleName_Type.h
     - Net_SignalRefl_Type.hpp

2.3.2 函数命名约定
------------------

.. code-block:: c++

   // 公共接口函数 - 使用模块前缀
   void Net_MsgRxInit(void);
   void Net_MsgRx_T10(void);
   
   // 类成员函数 - 使用e_前缀表示外部接口
   void e_Init_V(void);
   void e_Run_V(void);
   
   // 私有函数 - 使用l_前缀表示本地函数
   void l_CalcComNormalEnabCond_V(void);
   
   // 静态函数 - 使用m_前缀
   static void m_NetRunnableInit(void);

2.3.3 变量命名约定
------------------

.. code-block:: c++

   // 成员变量 - 使用m_前缀 + 类型后缀
   UW m_netDiagStartDelayCnt_uw;    // 无符号字
   B  m_netDiagStartRequest_b;      // 布尔型
   UB m_canChannel_ub;              // 无符号字节
   
   // 局部变量 - 使用l_前缀 + 类型后缀
   UW l_tempCounter_uw;
   B  l_isValid_b;
   
   // 全局变量 - 使用g_前缀
   extern SignalFormat_ST g_rx_signal_parameter[];

2.3.4 常量和宏定义
------------------

.. code-block:: c++

   // 宏定义 - 全大写，使用下划线分隔
   #define SIO_NET_ON              1
   #define SIO_NET_OFF             0
   #define NETCFG_CAN_PUB_USED     1
   
   // 常量 - 使用const关键字
   constexpr auto SIO_NET_SIG_INVAL_EVENT_ID_DEFAULT = (0xFFFFFFFFu);
   
   // 枚举 - 使用模块前缀
   typedef enum {
       Net_SignalFormatOK = 0,
       Net_SignalFormatComErr = 1
   } NetSignalFormatRet_En;

2.3.5 类型定义约定
------------------

.. code-block:: c++

   // 结构体 - 使用_ST后缀
   typedef struct {
       vfc::float32_t factor;
       vfc::float32_t offset;
   } SignalFormat_ST;
   
   // 枚举 - 使用_En后缀
   typedef enum {
       INVALID_VALUE_MULTI = 0,
       INVALID_VALUE_RANGE = 1
   } InvalidValueType_En;
   
   // 类 - 使用Pascal命名法
   class NetMode;
   class BusoffMode;
