==================================
SIO_NET 单元设计文档
==================================

.. meta::
   :description: SIO_NET网络通信组件单元设计文档
   :keywords: SIO_NET, CAN, 网络通信, 单元设计, 汽车电子

.. toctree::
   :maxdepth: 3
   :caption: 目录
   :numbered:

   01_document_overview
   02_definitions_abbreviations
   03_component_overview
   04_architecture_design
   05_interface_design
   06_function_design

版本信息
========

:版本: 1.0.0
:日期: 2025-06-24
:作者: SIO_NET开发团队
:状态: 草稿

文档摘要
========

本文档描述了SIO_NET网络通信组件的详细单元设计，包括组件架构、接口设计、函数实现等内容。
SIO_NET组件负责处理车辆CAN网络通信，提供消息收发、信号处理、E2E保护等功能。

主要特性
========

* **多CAN通道支持**: 支持公共CAN、私有CAN1/2/3等多个通道
* **E2E保护机制**: 提供端到端数据完整性保护
* **信号格式化**: 支持信号缩放、偏移、单位转换
* **诊断功能**: 集成DEM诊断事件管理
* **平台适配**: 支持多种车型平台配置

快速导航
========

* :doc:`01_document_overview` - 文档概述和适用范围
* :doc:`02_definitions_abbreviations` - 术语定义和缩略语
* :doc:`03_component_overview` - 组件功能概述
* :doc:`04_architecture_design` - 架构设计和行为描述
* :doc:`05_interface_design` - 接口设计和数据结构
* :doc:`06_function_design` - 函数详细设计

索引和表格
==========

* :ref:`genindex`
* :ref:`search`

.. note::
   本文档基于SIO_NET组件v1.0.0版本编写，如有更新请参考最新版本文档。

.. warning::
   本文档包含技术实现细节，仅供开发人员参考使用。
