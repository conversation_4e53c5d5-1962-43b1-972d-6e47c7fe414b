/**
 * @file SIO_Net_Adapter_Cfg.hpp
 * @brief SIO Network Adapter Common Configuration
 *
 * This file contains common configuration settings for the SIO Network
 * adapter that are shared across multiple vehicle platforms. It defines
 * feature enable/disable flags and common adapter parameters.
 *
 * <AUTHOR> Documentation
 * @date 2025-06-24
 * @version 1.0
 * @platform Common (All Platforms)
 */

#ifndef SIO_NET_ADAPTER_CFG_HPP
#define SIO_NET_ADAPTER_CFG_HPP

namespace sio_net {

/** @brief Feature enable flag value */
#define SIO_NET_ON              1

/** @brief Feature disable flag value */
#define SIO_NET_OFF             0

/** @brief TX signal array support configuration (enabled for common configuration) */
#define SUPPORT_TX_SIGNAL_ARRAY SIO_NET_ON

} // namespace sio_net

#endif