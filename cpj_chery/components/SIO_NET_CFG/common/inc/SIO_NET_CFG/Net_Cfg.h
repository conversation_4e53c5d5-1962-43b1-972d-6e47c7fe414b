/**
 * @file Net_Cfg.h
 * @brief Network Configuration Header
 *
 * This file contains the main network configuration settings for the SIO_NET
 * component, including CAN channel enable/disable switches, timing parameters,
 * bus-off recovery settings, and channel definitions.
 *
 * <AUTHOR> Documentation
 * @date 2025-06-24
 * @version 1.0
 */

#ifndef _NETCFG_H_
#define _NETCFG_H_

//-------------------------------------------------------------------
/// @brief CAN Channel Configuration
///
/// Set CAN channel switches according to project requirements:
/// - 1: Module enabled
/// - 0: Module disabled
//-------------------------------------------------------------------

/** @brief Enable/disable Public CAN channel */
#define NETCFG_CAN_PUB_USED 1

/** @brief Enable/disable Private CAN1 channel */
#define NETCFG_CAN_PR1_USED 1

/** @brief Enable/disable Private CAN2 channel */
#define NETCFG_CAN_PR2_USED 1

/** @brief Enable/disable Private CAN3 channel */
#define NETCFG_CAN_PR3_USED 0

//-------------------------------------------------------------------
/// @brief CAN Diagnostic Timing Configuration
///
/// Configure diagnostic delay times for network initialization and monitoring.
//-------------------------------------------------------------------

/** @brief Initial diagnostic delay time (in system ticks) */
#define SIO_NET_DIAGNOSTIC_INITIAL_TIME 300U

/** @brief Diagnostic start time after voltage confirmation (290ms + 100ms voltage confirm) */
#define SIO_NET_DIAGNOSTIC_START_TIME   290U

/** @brief Diagnostic restart time after bus-off recovery (40ms + 100ms voltage confirm) */
#define SIO_NET_DIAGNOSTIC_RESTART_TIME 40U

//-------------------------------------------------------------------
/// @brief CAN Bus-off Recovery Parameters
///
/// Configure parameters related to CAN bus-off detection and recovery.
//-------------------------------------------------------------------

/** @brief CAN State Manager bus-off recovery: none */
#define CANSM_BOR_NONE               0x00u

/** @brief Bus-off error code recovery delay time (in system ticks) */
#define BUSOFF_EC_RECOVER_DELAY_TIME 100U

/** @brief Invalid DEM event code for bus-off (used when channel is disabled) */
#define DemEC_BusOff_Invalid             0xFFu

//-------------------------------------------------------------------
/// @brief CAN Channel Definitions and DEM Event Mapping
///
/// Include required configuration headers and define channel-specific
/// communication manager channels and DEM event codes.
//-------------------------------------------------------------------
#include "ComM_Cfg.h"
#include "SDU_DAL_CFG/sdu_dal_cfg_DemDatabase.h"

#if (NETCFG_CAN_PUB_USED)
/** @brief Communication Manager channel ID for Public CAN */
#define COMMCHANNEL_CAN_PUB ComMConf_ComMChannel_CN_CAN_PUB_38d8db55
/** @brief DEM event code for Public CAN bus-off */
#define DemEC_BusOff_PUB    DemEC_BusOff_Public_Normal
#endif

#if (NETCFG_CAN_PR1_USED)
/** @brief Communication Manager channel ID for Private CAN1 */
#define COMMCHANNEL_CAN_PR1 ComMConf_ComMChannel_CN_CAN_PR1_be956d14
/** @brief DEM event code for Private CAN1 bus-off */
#define DemEC_BusOff_PR1    DemEC_BusOff_Private_Normal
#endif

#if (NETCFG_CAN_PR2_USED)
/** @brief Communication Manager channel ID for Private CAN2 */
#define COMMCHANNEL_CAN_PR2 ComMConf_ComMChannel_CN_CAN_PR2_279c3cae
/** @brief DEM event code for Private CAN2 bus-off */
#define DemEC_BusOff_PR2    DemEC_BusOff_Private_Normal
#endif

#if (NETCFG_CAN_PR3_USED)
/** @brief Communication Manager channel ID for Private CAN3 (placeholder) */
#define COMMCHANNEL_CAN_PR3
/** @brief DEM event code for Private CAN3 bus-off (invalid - channel disabled) */
#define DemEC_BusOff_PR3    DemEC_BusOff_Invalid
#endif

#endif /* _NETCFG_H_ */
