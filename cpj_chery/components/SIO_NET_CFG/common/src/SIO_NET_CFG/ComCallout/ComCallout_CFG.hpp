#ifndef COM_CALLOUT_CFG_HPP
#define COM_CALLOUT_CFG_HPP
#include "App_TypeDefine.hpp"

/**
 * @file ComCallout_CFG.hpp
 * @brief Communication Callout Configuration
 * @details This file contains configuration parameters and function declarations
 *          for communication callout functionality, including E2E protection
 *          parameters and CRC calculation functions.
 */

/**
 * @defgroup COM_CALLOUT_FUNCTIONS Communication Callout Functions
 * @brief Function declarations for communication callout operations
 * @{
 */

/**
 * @brief Calculate 8-bit CRC value
 * @details Computes CRC-8 checksum for given data buffer using specified
 *          initialization and XOR output values.
 * 
 * @param init_val Initial value for CRC calculation
 * @param xor_out XOR value applied to final CRC result
 * @param val Pointer to data buffer for CRC calculation
 * @param crc_byte Number of bytes to process in CRC calculation
 * 
 * @return Calculated CRC-8 value
 * 
 * @note This function is used for E2E protection mechanism in CAN communication
 * @see CAN_MSG_CRC_INIT_VALUE, CAN_MSG_CRC_XOR_OUT
 */
UB com_crc8(const UB init_val, const UB xor_out, const UB* val, UW crc_byte);

/** @} */ // end of COM_CALLOUT_FUNCTIONS group

/**
 * @defgroup E2E_CFG_PARAMS E2E Configuration Parameters
 * @brief End-to-End protection configuration parameters for CAN communication
 * @{
 */

/**
 * @brief CRC initial value for CAN message E2E protection
 * @details Initial value used for CRC calculation in E2E protection mechanism.
 *          This value is used as the starting point for CRC computation.
 */
const UB CAN_MSG_CRC_INIT_VALUE = 0x00u;

/**
 * @brief CRC XOR output value for CAN message E2E protection
 * @details XOR value applied to the final CRC result before transmission.
 *          Used to enhance CRC effectiveness in E2E protection.
 */
const UB CAN_MSG_CRC_XOR_OUT    = 0x00u;

/**
 * @brief Null index value for E2E counter
 * @details Special value indicating an invalid or uninitialized E2E counter index.
 *          Used for error detection and validation purposes.
 */
const UB CAN_E2E_INDEX_NULL     = 0xFEu;

/**
 * @brief Error indicator for E2E counter
 * @details Special value indicating an error condition in E2E counter processing.
 *          Used to signal counter-related errors in E2E protection.
 */
const UB CAN_E2E_CNT_ERROR      = 0xFFu;

/**
 * @brief Default value for E2E counter
 * @details Default initialization value for E2E counter when no specific
 *          value is available or during system initialization.
 */
const UB CAN_E2E_CNT_DEFAULT    = 0xAAu;

/**
 * @brief Maximum value for E2E counter
 * @details Upper limit for the E2E counter value. Counter wraps around
 *          to minimum value after reaching this maximum.
 * @note Counter range: 0x00 to 0x0F (4-bit counter)
 */
const UB CAN_E2E_CNT_MAX        = 0x0Fu;

/**
 * @brief Minimum value for E2E counter
 * @details Lower limit for the E2E counter value. This is the starting
 *          value after counter wrap-around from maximum.
 * @note Counter range: 0x00 to 0x0F (4-bit counter)
 */
const UB CAN_E2E_CNT_MIN        = 0x00u;

/**
 * @brief Maximum debounce counter value
 * @details Maximum count value for debouncing mechanism to filter out
 *          transient errors or noise in E2E protection validation.
 * @note Used to prevent false error detection due to temporary issues
 */
const UB DEBOUNCE_MAX_CNT       = 10u;

/** @} */ // end of E2E_CFG_PARAMS group

/**
 * @defgroup DATA_ID_CFG Data ID Configuration
 * @brief Configuration parameters for message data identification
 * @{
 */

/**
 * @brief Default data ID for SIO_NET communication messages
 * @details Default identifier used when no specific data ID is assigned
 *          to a communication message. Used as fallback value in message
 *          identification and validation processes.
 * @note Value 0xFFFF indicates an invalid or unassigned data ID
 */
const UWORD SIO_NET_COM_MSG_DEFAULT_DATA_ID = 0xFFFFu;

/** @} */ // end of DATA_ID_CFG group

#endif