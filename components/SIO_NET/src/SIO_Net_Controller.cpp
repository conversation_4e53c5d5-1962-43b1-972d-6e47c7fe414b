/**
 * @file SIO_Net_Controller.cpp
 * @brief SIO Network Controller Implementation
 *
 * This file implements the SIO Network Controller functionality for managing
 * CAN message transmission and reception through runnable lists.
 *
 * <AUTHOR> Documentation
 * @date 2025-06-24
 * @version 1.0
 */

#include "SIO_NET_CFG/NetReadX/GenData/SIO_Net_Rx_Msg_Instances.hpp"
#include "SIO_NET_CFG/SIO_Net_Tx_Msg_Instances.hpp"
#include "SIO_Net_Controller.hpp"

namespace sio_net {

// Legacy template instantiation comments - kept for reference
// template <typename T>
// T* RunnableList<T>::p_net_runnable_last = nullptr;

// template <typename T>
// T* RunnableList<T>::p_net_runnable_root = nullptr;

// sio_net::NET_RX_RunnableBase* sio_net::RunnableList<sio_net::NET_RX_RunnableBase>::p_net_runnable_last = nullptr;
// sio_net::NET_RX_RunnableBase* sio_net::RunnableList<sio_net::NET_RX_RunnableBase>::p_net_runnable_root = nullptr;

/**
 * @brief Initialize network runnable lists
 *
 * This static function initializes both the transmission and reception
 * runnable lists. It sets up the internal data structures needed for
 * network message processing.
 *
 * @note This is an internal function called by Net_MsgRxInit()
 */
static void m_NetRunnableInit()
{
    NET_TX_RunnableBase::init_list();
    NET_RX_RunnableBase::init_list();
}

/**
 * @brief Execute network runnable lists
 *
 * This static function executes both transmission and reception runnable
 * lists in sequence. TX runnables are executed first, followed by RX runnables.
 *
 * @note This is an internal function called by Net_MsgRx_T10()
 */
static void m_NetRunnableRun()
{
    // Execute network transmission runnables
    NET_TX_RunnableBase::run_list();
    // Execute network reception runnables
    NET_RX_RunnableBase::run_list();
}

} // namespace sio_net

extern "C" {

/**
 * @brief Initialize network message reception system (C interface)
 *
 * This function provides the C interface for initializing the network
 * message reception system. It calls the internal C++ initialization
 * function to set up the runnable lists.
 *
 * @see m_NetRunnableInit()
 */
void Net_MsgRxInit(void)
{
    sio_net::m_NetRunnableInit();
}

/**
 * @brief Execute network message processing (C interface)
 *
 * This function provides the C interface for executing the periodic
 * network message processing. It calls the internal C++ execution
 * function to run both TX and RX runnable lists.
 *
 * @see m_NetRunnableRun()
 */
void Net_MsgRx_T10(void)
{
    sio_net::m_NetRunnableRun();
}
}
