/**
 * @file Net_BusoffMode.h
 * @brief CAN Bus-off Mode Management
 *
 * This file defines the BusoffMode class which handles CAN bus-off detection,
 * recovery timing, and diagnostic event management for individual CAN channels.
 *
 * <AUTHOR> Documentation
 * @date 2025-06-24
 * @version 1.0
 */

#ifndef _BUSOFFMODE_H_
#define _BUSOFFMODE_H_

#include "App_TypeDefine.hpp"

namespace sio_net {

/**
 * @brief CAN Bus-off Mode Management Class
 *
 * The BusoffMode class manages bus-off detection and recovery for a single
 * CAN channel. It handles timing delays for bus-off recovery and manages
 * diagnostic events related to bus-off conditions.
 */
class BusoffMode
{
public:
    /**
     * @brief Default constructor
     *
     * Initializes the BusoffMode instance with default values:
     * - CAN channel: 0xFF (invalid)
     * - DEM EC bus-off index: 0xFF (invalid)
     * - Last bus-off state: 0xFF (invalid)
     * - Bus-off recovery delay counter: 0
     */
    BusoffMode()
    {
        m_canChannel_ub              = 0xFFu;
        m_demECBusoffIndex_ub        = 0xFFu;
        m_lastBusoffState_ub         = 0xFFu;
        m_busoffECRecoverDelayCnt_uw = 0x00u;
    }

    /**
     * @brief Destructor
     */
    ~BusoffMode() {}

public:
    /**
     * @brief Initialize bus-off mode management
     *
     * Initializes the bus-off mode management for a specific CAN channel
     * with the associated diagnostic event configuration.
     *
     * @param channel CAN channel identifier
     * @param ecIndex DEM event configuration index for bus-off events
     */
    void e_Init_V(UB channel, UB ecIndex);

    /**
     * @brief Execute bus-off mode management
     *
     * Performs periodic bus-off mode management tasks including:
     * - Bus-off state monitoring
     * - Recovery delay timing
     * - Diagnostic event reporting
     */
    void e_Run_V(void);

private:
    /** @brief CAN channel identifier */
    UB m_canChannel_ub;

    /** @brief DEM event configuration index for bus-off events */
    UB m_demECBusoffIndex_ub;

    /** @brief Last recorded bus-off state */
    UB m_lastBusoffState_ub;

    /** @brief Bus-off recovery delay counter */
    UW m_busoffECRecoverDelayCnt_uw;
};

} // namespace sio_net

#endif /* _BUSOFFMODE_H_ */
