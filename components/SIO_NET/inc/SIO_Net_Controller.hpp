/**
 * @file SIO_Net_Controller.hpp
 * @brief SIO Network Controller Interface
 *
 * This file provides the main interface for the SIO Network Controller,
 * which manages CAN message reception and transmission initialization
 * and periodic execution.
 *
 * <AUTHOR> Documentation
 * @date 2025-06-24
 * @version 1.0
 */

#ifndef SIO_NET_CONTROLLER_HPP
#define SIO_NET_CONTROLLER_HPP

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief Initialize network message reception system
 *
 * This function initializes the network message reception system by setting up
 * both TX and RX runnable lists. It should be called once during system startup
 * before any network communication begins.
 *
 * @note This function must be called before Net_MsgRx_T10()
 * @see Net_MsgRx_T10()
 */
extern void Net_MsgRxInit(void);

/**
 * @brief Execute network message processing (10ms cycle)
 *
 * This function executes the periodic network message processing for both
 * transmission and reception. It runs the TX and RX runnable lists in sequence.
 * This function should be called cyclically every 10ms.
 *
 * @note This function should be called after Net_MsgRxInit() has been executed
 * @see Net_MsgRxInit()
 */
extern void Net_MsgRx_T10(void);

#ifdef __cplusplus
}
#endif

#endif
