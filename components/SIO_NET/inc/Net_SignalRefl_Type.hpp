#ifndef NET_SIGNAL_FORMAT_TYPE_HPP
#define NET_SIGNAL_FORMAT_TYPE_HPP

#include "vfc/core/vfc_types.hpp"
#include "MetaMacro.hpp"
// PRQA S 1110, 1113 EOF
namespace sio_net {

/**
 * @brief Network signal format operation return codes
 * 
 * Enumeration defining the possible return values for signal format operations.
 */
enum NetSignalFormatRet_En
{
    Net_SignalFormatOK,         /**< Operation completed successfully */
    Net_SignalFormatListNULL,   /**< Signal format list is NULL */
    Net_SignalFormatValueNULL,  /**< Signal value is NULL */
    Net_SignalFormatSizeErr,    /**< Signal size error */
    Net_SignalFormatComErr,     /**< Communication error */
    Net_SignalFormatErr         /**< General signal format error */
};

/**
 * @brief Network message status enumeration
 * 
 * Defines the various status values that can be associated with network messages,
 * indicating the result of message processing and validation.
 */
enum NetMsgSatus_En : vfc::uint8_t
{
    Net_MSG_INIT        = 0u,   /**< Message in initial state */
    Net_MSG_E_OK        = 1u,   /**< Message processed successfully */
    Net_MSG_DLC_ERROR   = 2u,   /**< Data Length Code error */
    Net_MSG_CRC_ERROR   = 3u,   /**< CRC checksum error */
    Net_MSG_RC_ERROR    = 4u,   /**< Rolling counter error */
    Net_MSG_SECOC_ERROR = 5u,   /**< Secure communication error */
    Net_MSG_TO_ERROR    = 6u,   /**< Timeout error */
};

/**
 * @brief Function pointer type for signal invalid value checking
 * 
 * Type definition for callback functions that validate signal values.
 * 
 * @param signal_value Pointer to the signal value to be checked
 * @return bool True if the signal value is valid, false otherwise
 */
typedef bool (*NetSignalInvalCheckFunc)(const void* signal_value);

/**
 * @brief Invalid value type enumeration
 * 
 * Defines the different types of invalid value checks that can be performed.
 */
enum InvalidValueType
{
    INVALID_VALUE_MULTI,    /**< Multiple discrete invalid values */
    INVALID_VALUE_RANGE     /**< Invalid value range check */
};

/**
 * @brief Invalid value range type enumeration
 * 
 * Defines the range boundaries for invalid value checking.
 */
enum InvalidValueRange
{
    INVALID_VALUE_MIN,  /**< Minimum invalid value boundary */
    INVALID_VALUE_MAX   /**< Maximum invalid value boundary */
};

/**
 * @brief Structure for storing multiple invalid values
 * 
 * This structure is used to define a set of invalid values for signal validation.
 * It contains a count of values and a pointer to the array of invalid values.
 */
struct MultiValueType
{
    vfc::uint8_t         value_cnt;   /**< Number of invalid values in the list */
    const vfc::uint32_t* value_list;  /**< Pointer to array of invalid values */
};

/**
 * @brief Signal invalid value table structure
 * 
 * This structure defines the invalid value configuration for a signal.
 * It specifies the type of invalid value check (multi-value or range)
 * and contains the corresponding invalid value data.
 */
struct SignalInvalidValueTable_ST
{
    InvalidValueType type;        /**< Type of invalid value check (multi or range) */
    MultiValueType multi_value;   /**< Multi-value invalid value configuration */
};

/**
 * @brief Signal format configuration structure
 * 
 * This structure contains all the necessary parameters for signal format conversion
 * and validation, including scaling factors, offsets, CAN handle information,
 * and invalid value checking configuration.
 */
struct SignalFormat_ST
{
    float                             factor;           /**< Scaling factor for signal conversion */
    float                             offset;           /**< Offset value for signal conversion */
    float                             unit_factor;      /**< Unit conversion factor */
    vfc::uint32_t                     norming_factor;   /**< Normalization factor */
    vfc::uint32_t                     can_handle_id;    /**< CAN handle identifier */
    vfc::uint32_t                     inval_dem_id;     /**< Invalid value DEM event ID */
    NetSignalInvalCheckFunc           inval_check_fun;  /**< Invalid value check function pointer */
    const SignalInvalidValueTable_ST* inval_val_table;  /**< Pointer to invalid value table */
};

/**
 * @brief TX signal format configuration structure
 * 
 * This structure contains the configuration parameters specifically for 
 * transmit signal formatting, including value range limits and indexing.
 */
struct TXSignalFormat_ST
{
    float         factor;          /**< Scaling factor for TX signal conversion */
    float         offset;          /**< Offset value for TX signal conversion */
    float         unit_factor;     /**< Unit conversion factor */
    float         min_value;       /**< Minimum allowed signal value */
    float         max_value;       /**< Maximum allowed signal value */
    vfc::uint32_t norming_factor;  /**< Normalization factor */
    vfc::uint32_t can_handle_id;   /**< CAN handle identifier */
    vfc::uint32_t value_index;     /**< Signal value index */
};

/**
 * @brief Meta structure for TX signal reflection
 * 
 * Template structure used for compile-time reflection of TX signal structures.
 * Provides metadata about the fields in signal structures for automatic processing.
 * 
 * @tparam T The signal structure type to reflect
 */
template <typename>
struct StructMetaTxSig
{
    static constexpr size_t _field_count_ = 0u;  /**< Number of fields in the structure */
    template <typename, size_t>
    struct FIELD_EACH_MSG;  /**< Template for individual field metadata */
    
    /**
     * @brief Default field template specialization
     * 
     * @tparam T The structure type
     */
    template <typename T>
    struct FIELD_EACH_MSG<T, 0>
    {
    };
};

/**
 * @brief Macro for network message reflection
 * 
 * This macro generates template specialization for a given structure type
 * to enable compile-time reflection of its fields.
 * 
 * @param st The structure type to reflect
 * @param ... Variable arguments representing the fields to reflect
 */
#define REFL_NET_MSG(st, ...)                                                                                          \
    template <>                                                                                                        \
    struct StructMetaTxSig<st>                                                                                         \
    {                                                                                                                  \
        template <typename, size_t>                                                                                    \
        struct FIELD_EACH_MSG;                                                                                         \
        static constexpr size_t _field_count_ = GET_ARG_COUNT(__VA_ARGS__);                                            \
        PASTE(REPEAT_, GET_ARG_COUNT(__VA_ARGS__))(FIELD_EACH_MSG, 0, __VA_ARGS__)                                     \
    }

/**
 * @brief Macro for defining network signal field reflection
 * 
 * Creates a tuple of field name and its corresponding parameter name.
 * 
 * @param msg_name The message name prefix
 * @param field The field name
 */
#define FIELD_NET_SIGNAL(msg_name, field)         (field, msg_name##_##field)

/** @brief Macro to extract field arguments from tuple */
#define GET_FIELD_ARGS(args, ...)                 SPLIT_ARGS_FIELD args __VA_ARGS__

/** @brief Macro to extract index arguments from tuple */
#define GET_INDEX_ARGS(args, ...)                 SPLIT_ARGS_INDEX args __VA_ARGS__

/** @brief Macro to split arguments and extract field part */
#define SPLIT_ARGS_FIELD(field, param_index, ...) field __VA_ARGS__

/** @brief Macro to split arguments and extract index part */
#define SPLIT_ARGS_INDEX(field, param_index, ...) param_index __VA_ARGS__

/**
 * @brief Macro for generating field metadata template
 * 
 * Generates template specialization for each field in the reflected structure.
 * 
 * @param i The field index
 * @param args The field arguments tuple
 */
#define FIELD_EACH_MSG(i, args)                                                                                        \
    template <typename T>                                                                                              \
    struct FIELD_EACH_MSG<T, i>                                                                                        \
    {                                                                                                                  \
        decltype(auto) value()                                                                                         \
        {                                                                                                              \
            return (&T::GET_FIELD_ARGS(args));                                                                         \
        }                                                                                                              \
        template <typename U, typename = void>                                                                         \
        struct HasIndex                                                                                                \
        {                                                                                                              \
            static constexpr int value = -1;                                                                           \
        };                                                                                                             \
        template <typename U>                                                                                          \
        struct HasIndex<U, std::void_t<decltype(U::GET_INDEX_ARGS(args))>>                                             \
        {                                                                                                              \
            static constexpr int value = U::GET_INDEX_ARGS(args);                                                      \
        };                                                                                                             \
        decltype(auto) parameter()                                                                                     \
        {                                                                                                              \
            constexpr int index = HasIndex<ParameterIndex>::value;                                                     \
            return index;                                                                                              \
        };                                                                                                             \
    };

} // namespace sio_net

#endif