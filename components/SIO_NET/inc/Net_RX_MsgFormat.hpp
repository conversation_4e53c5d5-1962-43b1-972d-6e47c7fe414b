/**
 * @file Net_RX_MsgFormat.hpp
 * @brief CAN RX Message Format Processing
 *
 * This file provides template functions and utilities for processing received
 * CAN messages, including signal formatting, invalid value checking, and
 * diagnostic event reporting. It handles the conversion from raw CAN data
 * to application-level signal values.
 *
 * <AUTHOR> Documentation
 * @date 2025-06-24
 * @version 1.0
 */

#include "SIO_NET/inc/NET_RX_MsgDDKPortType.hpp"
#include "SIO_NET_CFG/NetReadX/GenData/SIO_Net_Rx_Signal_Adapter.hpp"
#include "SIO_NET/inc/Net_SignalFormat.hpp"
#include "Net_SignalRefl.hpp"
#include "sdu_dal_RteServices.h"
extern "C" {
uint8_t Com_ReceiveSignal(uint32_t SignalId, void* SignalDataPtr);
}

#include "util_normalize.hpp"
namespace sio_net {

/**
 * @brief Format received CAN signal value
 *
 * Template function to format a raw CAN signal value according to the
 * specified signal format parameters. Applies scaling factors, offsets,
 * unit conversions, and normalization as needed.
 *
 * @tparam T Target value type (output)
 * @tparam U Source value type (input)
 * @param parameter Signal format parameters containing scaling factors and offsets
 * @param value Reference to the output value to be formatted
 * @param value_raw Raw input value from CAN message
 */
template <typename T, typename U>
void m_NetRxSignalFormat(const SignalFormat_ST parameter, T& value, const U value_raw)
{
    const vfc::float32_t epsilon  = 1e-6f;
    vfc::float32_t       t_temp_f = static_cast<vfc::float32_t>(value_raw);

    // Apply scaling factor if non-zero
    if (parameter.factor > epsilon)
        t_temp_f = t_temp_f * (parameter.factor);

    // Apply offset
    t_temp_f += parameter.offset;

    // Apply unit conversion factor if non-zero
    if (parameter.unit_factor > epsilon)
        t_temp_f *= (parameter.unit_factor);

    // Convert to target type with optional normalization
    if constexpr (std::is_floating_point_v<T>)
        value = static_cast<T>(t_temp_f);
    else
    {
        if (parameter.norming_factor != 0u)
            value = utility::norm::Normalize<T>(t_temp_f, parameter.norming_factor);
        else
            value = static_cast<T>(t_temp_f);
    }
}
/**
 * @brief Check if value is in invalid value table
 *
 * Static function to check if a signal value matches any of the invalid
 * values defined in the signal's invalid value table. Supports both
 * discrete invalid values and invalid value ranges.
 *
 * @param inval_val_table Pointer to the invalid value table structure
 * @param value Signal value to check
 * @return true if value is invalid, false if value is valid
 */
static bool m_NetCheckInvalidValueTable(const SignalInvalidValueTable_ST* inval_val_table, const vfc::uint32_t value)
{
    if (inval_val_table->type == InvalidValueType::INVALID_VALUE_MULTI)
    {
        // Check against multiple discrete invalid values
        for (int i = 0; i < inval_val_table->multi_value.value_cnt; i++)
        {
            if (value == inval_val_table->multi_value.value_list[i])
            {
                return true;
            }
        }
    }
    else if (inval_val_table->type == InvalidValueType::INVALID_VALUE_RANGE)
    {
        // Check if value is within invalid range
        if ((inval_val_table->multi_value.value_list[InvalidValueRange::INVALID_VALUE_MIN] <= value)
            && (value <= inval_val_table->multi_value.value_list[InvalidValueRange::INVALID_VALUE_MAX]))
        {
            return true;
        }
    }
    return false;
}
/**
 * @brief Perform invalid value check for received signal
 *
 * Static function to check if a received signal value is invalid according
 * to the signal's configuration. Reports the validation result to the
 * Diagnostic Event Manager (DEM) for fault monitoring.
 *
 * @param parameter Signal format parameters containing validation configuration
 * @param value Signal value to validate
 */
static void m_NetRxSignalInvalCheck(const SignalFormat_ST parameter, const vfc::uint32_t value)
{
    bool is_invalid = false;

    // Check invalid value using custom function or table
    if (parameter.inval_check_fun != nullptr)
    {
        // Use custom validation function if available
        is_invalid = parameter.inval_check_fun(&value);
    }
    else if (parameter.inval_val_table != nullptr)
    {
        // Use invalid value table for validation
        is_invalid = m_NetCheckInvalidValueTable(parameter.inval_val_table, value);
    }

    // Report invalid status to DEM for diagnostic monitoring
    if (is_invalid)
    {
        (void)RTE_CALL_DEMADAPT_SET_EVENTSTATUS(parameter.inval_dem_id, sdu_dal::EventStatus::Prefailed);
    }
    else
    {
        (void)RTE_CALL_DEMADAPT_SET_EVENTSTATUS(parameter.inval_dem_id, sdu_dal::EventStatus::Prepassed);
    }
}
/**
 * @brief Helper function to receive CAN signal
 *
 * Template helper function to handle Com_ReceiveSignal API call and
 * perform basic type conversion from source to target value type.
 *
 * @tparam T Source value type (raw CAN data type)
 * @tparam U Target value type (application data type)
 * @param value Reference to signal structure containing both source and target values
 * @param parameter_index Index into the signal parameter array
 * @return uint8_t Result from Com_ReceiveSignal (0 = success, non-zero = error)
 */
template <typename T, typename U>
uint8_t receive_signal(NET_SIG_ST<T, U>& value, int parameter_index)
{
    uint8_t result = Com_ReceiveSignal(m_rx_signal_parameter[parameter_index].can_handle_id, &value.src_value);
    if (result == 0u)
    {
        value.value = static_cast<U>(value.src_value);
    }
    return result;
}
/**
 * @brief Format individual signal from CAN message
 *
 * Template function to format an individual signal from a received CAN message.
 * Handles signal reception, invalid value checking, and format conversion.
 *
 * @tparam T Signal object type (must be a format base type)
 * @param obj Signal object to be formatted (forwarding reference)
 * @param parameter_index Index into the signal parameter array (-1 if not configured)
 */
template <typename T>
void format_sig(T&& obj, int parameter_index)
{
    if constexpr (IsFormaBaseType<std::decay_t<T>>::value)
    {
        if (parameter_index != -1)
        {
            NetSignalFormatRet_En      ret = Net_SignalFormatOK;
            std::remove_reference_t<T> value_raw{};

            // Receive signal from CAN communication stack
            if (receive_signal(value_raw, parameter_index) != 0u) // E_OK = 0
            {
                // ComIPduGroup is not active or other communication error
                ret = Net_SignalFormatComErr;
            }
            else
            {
                // Perform invalid value check if configured
                if (m_rx_signal_parameter[parameter_index].inval_dem_id != SIO_NET_SIG_INVAL_EVENT_ID_DEFAULT)
                {
                    m_NetRxSignalInvalCheck(
                        m_rx_signal_parameter[parameter_index], static_cast<vfc::uint32_t>(value_raw.src_value));
                }
                // Apply signal formatting (scaling, offset, etc.)
                m_NetRxSignalFormat(m_rx_signal_parameter[parameter_index], obj.value, value_raw.value);
            }
            obj.status = ret;
        }
    }
    else
    {
        static_assert(IsFormaBaseType<std::decay_t<T>>::value, "T is not a IsFormaBaseType type");
    }
}

#if (ZX_COMPUTE_TARGET == MCU)
/**
 * @brief Signal receive method for CAN message template
 *
 * Template method to receive and format all signals in a CAN message.
 * Uses a lambda function to apply signal formatting to each signal
 * in the message structure.
 *
 * @tparam ParaType Message parameter type
 * @param l_msg_ref Reference to the message structure to be populated
 */
template <typename ParaType>
void CanMsgTemp<ParaType>::signal_receive(ParaType& l_msg_ref)
{
    auto lam_fun = [&](auto& val, int parameter_index) { format_sig(val, parameter_index); };
    g_NetFormatMsg(l_msg_ref, lam_fun);
}
#endif

} // namespace sio_net
