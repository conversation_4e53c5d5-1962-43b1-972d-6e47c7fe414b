/**
 * @file Net_SignalFormat.hpp
 * @brief Network Signal Format Definitions and Utilities
 *
 * This file provides signal format definitions, invalid value handling,
 * and utility macros for CAN signal processing. It includes templates
 * and constants for signal validation and format parameter management.
 *
 * <AUTHOR> Documentation
 * @date 2025-06-24
 * @version 1.0
 */

#ifndef NET_SIGNAL_FORMAT_HPP
#define NET_SIGNAL_FORMAT_HPP

#include "SIO_NET/inc/IF_SIO_NET_x_ValueType.hpp"
#include "Net_SignalRefl_Type.hpp"
// PRQA S 1110, 1113 EOF
namespace sio_net {

/** @brief Default event ID for signals without invalid value checking */
constexpr auto SIO_NET_SIG_INVAL_EVENT_ID_DEFAULT = (0xFFFFFFFFu);

/**
 * @brief Get signal invalid value table name
 *
 * Macro to generate the invalid value table name for a specific message and signal.
 */
#define GET_SIGNAL_INVAL_VALUE_TABLE(msg, signal)     msg##signal##_inval_val_table

/**
 * @brief Get pointer to signal invalid value table
 *
 * <PERSON><PERSON> to generate a pointer to the invalid value table for a specific message and signal.
 */
#define GET_SIGNAL_INVAL_VALUE_TABLE_PTR(msg, signal) (&(msg##signal##_inval_val_table))

/**
 * @brief Create invalid value list
 *
 * Macro to create a list of invalid values using variadic arguments.
 */
#define CREAT_INVAL_LIST(...)                         __VA_ARGS__

/**
 * @brief Create invalid value table for multiple discrete values
 *
 * Macro to create an invalid value table that contains multiple discrete
 * invalid values for a specific message signal.
 *
 * @param msg Message name
 * @param signal Signal name
 * @param ... Variable arguments representing invalid values
 */
#define CREAT_INVAL_TABLE(msg, signal, ...)                                                                            \
    constexpr vfc::uint32_t  msg##signal##_inval_val_list[] = {__VA_ARGS__};                                           \
    constexpr MultiValueType msg##signal##multi_value       = {                                                        \
        sizeof(msg##signal##_inval_val_list)/sizeof(msg##signal##_inval_val_list[0]), msg##signal##_inval_val_list};   \
    constexpr SignalInvalidValueTable_ST GET_SIGNAL_INVAL_VALUE_TABLE(msg, signal) = {                                 \
        InvalidValueType::INVALID_VALUE_MULTI, msg##signal##multi_value};

/**
 * @brief Create invalid event (placeholder)
 *
 * Placeholder macro for creating invalid events. Currently empty implementation.
 *
 * @param msg Message name
 * @param signal Signal name
 * @param event_id Event identifier
 */
#define CREAT_INVAL_EVENT(msg, signal, event_id)

/**
 * @brief Create invalid value table for a range
 *
 * Macro to create an invalid value table that defines a range of invalid values
 * for a specific message signal.
 *
 * @param msg Message name
 * @param signal Signal name
 * @param min Minimum invalid value (inclusive)
 * @param max Maximum invalid value (inclusive)
 */
#define CREAT_INVAL_RANGE(msg, signal, min, max)                                                                       \
    constexpr vfc::uint32_t  msg##signal##_inval_val_list[] = {min, max};                                           \
    constexpr MultiValueType msg##signal##multi_value       = {                                                        \
        sizeof(msg##signal##_inval_val_list)/sizeof(msg##signal##_inval_val_list[0]), msg##signal##_inval_val_list};   \
    constexpr SignalInvalidValueTable_ST GET_SIGNAL_INVAL_VALUE_TABLE(msg, signal) = {                                 \
        InvalidValueType::INVALID_VALUE_RANGE, msg##signal##multi_value};

/**
 * @brief Create signal format parameter structure
 *
 * Macro to create a signal format parameter structure with all necessary
 * configuration values for signal processing, including scaling factors,
 * offsets, CAN handle ID, invalid event ID, and validation functions.
 *
 * @param msg_name Message name
 * @param sig_name Signal name
 */
#define FIELD_SIGNAL_FORMAT_PARAMETER(msg_name, sig_name)                                                              \
    {                                                                                                                  \
        SIO_NET_SIG_##msg_name##_##sig_name##_FACTOR, SIO_NET_SIG_##msg_name##_##sig_name##_OFFSET,                    \
            SIO_NET_SIG_##msg_name##_##sig_name##_UNIT_FACTOR, SIO_NET_SIG_##msg_name##_##sig_name##_NORMING_FACTOR,   \
            SIO_NET_SIG_##msg_name##_##sig_name##_CAN_HANDLE_ID, SIO_NET_SIG_##msg_name##_##sig_name##_INVAL_EVENT_ID, \
            SIO_NET_SIG_##msg_name##_##sig_name##_INVAL_CHECK_FUN,                                                     \
            SIO_NET_SIG_##msg_name##_##sig_name##_INVAL_VALUE_TABLE                                                    \
    }

} // namespace sio_net
#endif
