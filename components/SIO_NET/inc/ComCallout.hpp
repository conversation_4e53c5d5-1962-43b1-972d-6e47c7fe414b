/**
 * @file ComCallout.hpp
 * @brief CAN Communication Callout Functions and E2E Protection
 *
 * This file provides CAN communication callout functions, E2E (End-to-End)
 * protection mechanisms, and utility functions for CAN message processing.
 * It includes structures and templates for handling segmented messages,
 * counter validation, and CRC checking.
 *
 * <AUTHOR> Documentation
 * @date 2025-06-24
 * @version 1.0
 */

#ifndef COM_CALLOUT_HPP
#define COM_CALLOUT_HPP
#include "SIO_NET_CFG/ComCallout/ComCallout_CFG.hpp"
#include "Net_SignalRefl_Type.hpp"
#include "MetaMacro.hpp"
#include "sdu_dal_RteServices.h"
extern "C" {
#include "Com_Types.h"
#include "SIO_NET_CFG/Net_Cfg.h"
}
// PRQA S 1110, 1113 EOF

/** @brief Default event ID for invalid events */
#define DEFAULT_EVENT 0xFFFFu

/** @brief Weak function attribute for overridable functions */
#define WEAK_FUN      __attribute__((weak))
/**
 * @brief CAN Segment E2E Information Structure
 *
 * Contains End-to-End protection information for a single CAN message segment,
 * including data ID, offset, counter bit position, and CRC offset.
 */
struct CANSegment_E2E_Info
{
    const UW data_id;       /**< @brief Data identifier for the segment */
    const UW seg_offset;    /**< @brief Byte offset of the segment in the message */
    const UW cnt_bit_start; /**< @brief Bit position where the counter starts */
    const UW crc_offset;    /**< @brief Byte offset where the CRC is located */
};

/**
 * @brief CAN Message E2E Configuration Structure
 *
 * Contains End-to-End protection configuration for a complete CAN message,
 * including segment information, counter buffer, and message length.
 */
struct CANMsg_E2E_Cfg
{
    const UB                   seg_total; /**< @brief Total number of segments in the message */
    const CANSegment_E2E_Info* seg_info;  /**< @brief Pointer to array of segment information */
    UB*                        cnt_buff;  /**< @brief Pointer to counter buffer array */
    UW                         msg_len;   /**< @brief Total message length in bytes */
};
/**
 * @brief CAN RX Segment E2E Information Structure
 *
 * Contains End-to-End protection information for a single received CAN message segment,
 * including data ID, offset, counter bit position, CRC offset, and event IDs.
 */
struct CANRxSegment_E2E_Info
{
    const UW data_id;       /**< @brief Data identifier for the segment */
    const UW seg_offset;    /**< @brief Byte offset of the segment in the message */
    const UW cnt_bit_start; /**< @brief Bit position where the counter starts */
    const UW crc_offset;    /**< @brief Byte offset where the CRC is located */
    const UW crc_event;     /**< @brief Event ID for CRC errors */
    const UW rc_event;      /**< @brief Event ID for rolling counter errors */
};

/**
 * @brief CAN RX Message E2E Configuration Structure
 *
 * Contains End-to-End protection configuration for a complete received CAN message,
 * including segment information, counter buffer, message length, timeout event,
 * and callback functions for status updates.
 */
struct CANRxMsg_E2E_Cfg
{
    const UB                     seg_total;      /**< @brief Total number of segments in the message */
    const CANRxSegment_E2E_Info* seg_info;       /**< @brief Pointer to array of segment information */
    UB*                          cnt_buff;       /**< @brief Pointer to counter buffer array */
    UW                           msg_len;        /**< @brief Total message length in bytes */
    const UW                     to_event;       /**< @brief Event ID for timeout errors */
    void (*set_msg_status)(const sio_net::NetMsgSatus_En); /**< @brief Callback to set message status */
    void (*set_update_flag)(const vfc::uint8_t);           /**< @brief Callback to set update flag */
    UB* dlc_cnt;                                            /**< @brief Pointer to DLC counter */
};
#define CNT_BUFF_DEFAULT_VALUE_1 CAN_E2E_CNT_DEFAULT
#define CNT_BUFF_DEFAULT_VALUE_2 CAN_E2E_CNT_DEFAULT, CNT_BUFF_DEFAULT_VALUE_1
#define CNT_BUFF_DEFAULT_VALUE_3 CAN_E2E_CNT_DEFAULT, CNT_BUFF_DEFAULT_VALUE_2
#define CNT_BUFF_DEFAULT_VALUE_4 CAN_E2E_CNT_DEFAULT, CNT_BUFF_DEFAULT_VALUE_3
#define CNT_BUFF_DEFAULT_VALUE_5 CAN_E2E_CNT_DEFAULT, CNT_BUFF_DEFAULT_VALUE_4
#define CNT_BUFF_DEFAULT_VALUE_6 CAN_E2E_CNT_DEFAULT, CNT_BUFF_DEFAULT_VALUE_5
#define CNT_BUFF_DEFAULT_VALUE_7 CAN_E2E_CNT_DEFAULT, CNT_BUFF_DEFAULT_VALUE_6
#define CNT_BUFF_DEFAULT_VALUE_8 CAN_E2E_CNT_DEFAULT, CNT_BUFF_DEFAULT_VALUE_7

#define FIELD_SEG_INFO(msg_name, index)                                                                                \
    {SIO_NET_COM_MSG_##msg_name##_SEG##index##_DATA_ID,                                                                \
     SIO_NET_COM_MSG_##msg_name##_SEG##index##_OFFSET,                                                                 \
     SIO_NET_COM_MSG_##msg_name##_SEG##index##_CNT_BIT,                                                                \
     SIO_NET_COM_MSG_##msg_name##_SEG##index##_CRC_OFFSET}
#define FIELD_RX_SEG_INFO(msg_name, index)                                                                             \
    {SIO_NET_COM_MSG_##msg_name##_SEG##index##_DATA_ID,                                                                \
     SIO_NET_COM_MSG_##msg_name##_SEG##index##_OFFSET,                                                                 \
     SIO_NET_COM_MSG_##msg_name##_SEG##index##_CNT_BIT,                                                                \
     SIO_NET_COM_MSG_##msg_name##_SEG##index##_CRC_OFFSET,                                                             \
     SIO_NET_COM_MSG_##msg_name##_SEG##index##_EVENT_CRC,                                                              \
     SIO_NET_COM_MSG_##msg_name##_SEG##index##_EVENT_RC}

#define CREAT_MSG_SEG_INFO(msg_name, ...)      __can_##msg_name##_seg_info[] = {__VA_ARGS__}
#define CREAT_MSG_CNT_BUFF(msg_name, buff_len) __can_##msg_name##_cnt_buff[] = {CNT_BUFF_DEFAULT_VALUE_##buff_len}

#define DEFINE_MSG_RX_E2E_INFO(msg_name)                                                                               \
    static void __can_##msg_name##set_msg_status(const sio_net::NetMsgSatus_En status)                                 \
    {                                                                                                                  \
        SIO_NET_COM_MSG_##msg_name##_RUNNABLE.set_msg_status(status);                                                  \
    }                                                                                                                  \
    static void __can_##msg_name##set_update_flag(const vfc::uint8_t flag)                                             \
    {                                                                                                                  \
        SIO_NET_COM_MSG_##msg_name##_RUNNABLE.set_update_flag(flag);                                                   \
    }                                                                                                                  \
    static UB __can_##msg_name##_dlc_cnt = 0u;

#define CREAT_MSG_SEG(type, msg_name, buff_len, ...)                                                                   \
    const type CREAT_MSG_SEG_INFO(msg_name, __VA_ARGS__);                                                              \
    static UB  CREAT_MSG_CNT_BUFF(msg_name, buff_len);

#define INIT_MSG_RX_E2E_CFG(msg_name, seg_total)                                                                       \
    {seg_total,                                                                                                        \
     __can_##msg_name##_seg_info,                                                                                      \
     __can_##msg_name##_cnt_buff,                                                                                      \
     SIO_NET_COM_MSG_##msg_name##_MSGLEN,                                                                              \
     SIO_NET_COM_MSG_##msg_name##_EVENT_TO,                                                                            \
     &__can_##msg_name##set_msg_status,                                                                                \
     &__can_##msg_name##set_update_flag,                                                                               \
     &__can_##msg_name##_dlc_cnt}
#define INIT_MSG_RX_E2E_CFG_NO_SEG(msg_name)                                                                           \
    {0u,                                                                                                               \
     nullptr,                                                                                                          \
     nullptr,                                                                                                          \
     SIO_NET_COM_MSG_##msg_name##_MSGLEN,                                                                              \
     SIO_NET_COM_MSG_##msg_name##_EVENT_TO,                                                                            \
     &__can_##msg_name##set_msg_status,                                                                                \
     &__can_##msg_name##set_update_flag,                                                                               \
     &__can_##msg_name##_dlc_cnt}

#define INIT_MSG_TX_E2E_CFG(msg_name, seg_total)                                                                       \
    {seg_total, __can_##msg_name##_seg_info, __can_##msg_name##_cnt_buff, SIO_NET_COM_MSG_##msg_name##_MSGLEN}
#define INIT_MSG_TX_E2E_CFG_NO_SEG(msg_name) {0u, nullptr, nullptr, SIO_NET_COM_MSG_##msg_name##_MSGLEN}

/**
 * @brief Validate message length against configuration
 *
 * Template function to validate if the received message length matches
 * the expected length defined in the configuration. The validation logic
 * differs based on the vehicle platform.
 *
 * @tparam T Configuration structure type (CANMsg_E2E_Cfg or CANRxMsg_E2E_Cfg)
 * @param cfg Pointer to the E2E configuration structure
 * @param msg_len Actual received message length
 * @return true if message length is valid, false otherwise
 */
template <typename T>
inline bool _IS_MsgLenValid(const T* cfg, UW msg_len)
{
    bool t_Return_b = true;
    if (cfg == nullptr)
    {
        return false;
    }
#if defined(ZX_CPJ_VEH_CHERY_ICAR05)
    // For ICAR05: exact length match required
    if (cfg->msg_len != msg_len)
#else
    // For other platforms: received length must not exceed configured length
    if (cfg->msg_len > msg_len)
#endif
    {
        t_Return_b = false;
    }

    return t_Return_b;
}
/**
 * @brief Set counter value in CAN message data
 *
 * Sets the E2E rolling counter value in the CAN message data at the specified
 * bit position. Supports different bit alignments (0, 2, 4 bit offsets).
 *
 * @param seg_info Segment information containing counter bit position
 * @param data Pointer to the CAN message data buffer
 * @param cnt Counter value to set (4-bit value)
 */
inline void _SetCntFormMsg(const CANSegment_E2E_Info seg_info, UBYTE* data, UB cnt)
{
    if ((seg_info.cnt_bit_start & 0x07u) == (UW)0u)
    {
        // Counter starts at bit 0 (lower nibble)
        data[seg_info.cnt_bit_start >> 3u] = (data[seg_info.cnt_bit_start >> 3] & 0xF0u) | (cnt & 0x0Fu);
    }
    else if ((seg_info.cnt_bit_start & 0x07u) == (UW)0x04u)
    {
        // Counter starts at bit 4 (upper nibble)
        data[seg_info.cnt_bit_start >> 3u] = (data[seg_info.cnt_bit_start >> 3] & 0x0Fu) | ((cnt & 0x0Fu) << 4);
    }
    else if ((seg_info.cnt_bit_start & 0x07u) == (UW)0x02u)
    {
        // Counter starts at bit 2 (middle 4 bits)
        data[seg_info.cnt_bit_start >> 3u] = (data[seg_info.cnt_bit_start >> 3] & 0xC3u) | ((cnt & 0x0Fu) << 2);
    }
    else
    {
        // Unsupported bit position - do nothing
    }
}
/**
 * @brief Update counter values in CAN message data
 *
 * Template function to update E2E rolling counter values for all segments
 * in a CAN message. Increments counters and sets them in the message data.
 *
 * @tparam T Configuration structure type (CANMsg_E2E_Cfg or CANRxMsg_E2E_Cfg)
 * @param cfg Pointer to the E2E configuration structure
 * @param data Pointer to the CAN message data buffer
 */
template <typename T>
inline void _UpdateCountValue(const T* cfg, UBYTE* data)
{
    if ((cfg == nullptr) || (data == nullptr))
    {
        return;
    }
    for (UB index = 0u; index < cfg->seg_total; index++)
    {
        if (cfg->seg_info[index].cnt_bit_start == CAN_E2E_INDEX_NULL)
        {
            continue;
        }
#ifndef NETCFG_RC_NOTIFICAITON_USED
        // Increment counter with wrap-around
        cfg->cnt_buff[index] += 1u;
        if (cfg->cnt_buff[index] > CAN_E2E_CNT_MAX)
        {
            cfg->cnt_buff[index] = CAN_E2E_CNT_MIN;
        }
#endif
        // Set the updated counter value in the message data
        _SetCntFormMsg(cfg->seg_info[index], data, cfg->cnt_buff[index]);
    }
}

/**
 * @brief Update signal counter values
 *
 * Template function to update E2E rolling counter values for all segments
 * without modifying the actual message data. Used for signal-level counter
 * management.
 *
 * @tparam T Configuration structure type (CANMsg_E2E_Cfg or CANRxMsg_E2E_Cfg)
 * @param cfg Pointer to the E2E configuration structure
 */
template <typename T>
inline void _UpdateSigCountValue(const T* cfg)
{
    for (UB index = 0u; index < cfg->seg_total; index++)
    {
        if (cfg->seg_info[index].cnt_bit_start == CAN_E2E_INDEX_NULL)
        {
            continue;
        }
        // Increment counter with wrap-around
        cfg->cnt_buff[index] += 1u;
        if (cfg->cnt_buff[index] > CAN_E2E_CNT_MAX)
        {
            cfg->cnt_buff[index] = CAN_E2E_CNT_MIN;
        }
    }
}

#endif