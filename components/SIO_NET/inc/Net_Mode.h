/**
 * @file Net_Mode.h
 * @brief Network Mode Management
 *
 * This file defines the NetMode class which manages network communication
 * and diagnostic modes, including CAN bus-off recovery and network state control.
 *
 * <AUTHOR> Documentation
 * @date 2025-06-24
 * @version 1.0
 */

#ifndef _NETMODE_H_
#define _NETMODE_H_

#include "SIO_NET/inc/Net_BusoffMode.h"
#include "App_TypeDefine.hpp"
#include "SIO_NET_CFG/Net_Cfg.h"

namespace sio_net {

/**
 * @brief Network Mode Management Class
 *
 * The NetMode class manages the overall network communication state,
 * including diagnostic timing, communication enable/disable control,
 * and bus-off recovery for multiple CAN channels.
 */
class NetMode
{

public:
    /**
     * @brief Default constructor
     *
     * Initializes the NetMode instance with default values:
     * - Diagnostic start delay counter: 0
     * - Diagnostic start request: FALSE
     * - Network run request: FALSE
     */
    NetMode(void) : m_netDiagStartDelayCnt_uw(0u), m_netDiagStartRequest_b(FALSE), m_netRunRequest_b(FALSE) {}

    /**
     * @brief Destructor
     */
    ~NetMode() {}

public:
    /**
     * @brief Initialize network mode
     *
     * Initializes the network mode management system and all associated
     * bus-off mode instances for configured CAN channels.
     */
    void e_Init_V(void);

    /**
     * @brief Execute network mode management
     *
     * Performs periodic network mode management tasks including diagnostic
     * timing control and communication condition calculation.
     */
    void e_Run_V(void);

    /**
     * @brief Enable network communication
     *
     * Enables network communication by setting the appropriate internal flags
     * and notifying the communication management system.
     */
    void e_EnableNetCom_V(void);

    /**
     * @brief Disable network communication
     *
     * Disables network communication by clearing the appropriate internal flags
     * and notifying the communication management system.
     */
    void e_DisableNetCom_V(void);

    /**
     * @brief Enable network diagnostics
     *
     * Enables network diagnostic functionality and starts the diagnostic
     * delay timing mechanism.
     */
    void e_EnableNetDiag_V(void);

    /**
     * @brief Disable network diagnostics
     *
     * Disables network diagnostic functionality and stops the diagnostic
     * delay timing mechanism.
     */
    void e_DisableNetDiag_V(void);

    /**
     * @brief Set diagnostic restart time
     *
     * Sets the diagnostic delay counter to the restart time value,
     * typically used after a bus-off recovery event.
     */
    void e_SetDiagRestartTime(void);

    /**
     * @brief Set diagnostic start time
     *
     * Sets the diagnostic delay counter to the start time value,
     * typically used during initial system startup.
     */
    void e_SetDiagStartTime(void);

    /**
     * @brief Set network run request flag
     *
     * Sets the internal network run request flag to the specified value.
     *
     * @param value Boolean value to set (TRUE to enable, FALSE to disable)
     */
    void e_SetNetRunRequest_V(B value) { m_netRunRequest_b = value; }

    /**
     * @brief Get network run request flag
     *
     * Returns the current state of the network run request flag.
     *
     * @return Current network run request state (TRUE if enabled, FALSE if disabled)
     */
    B    e_GetNetRunRequest_B(void) { return m_netRunRequest_b; }

private:
    /**
     * @brief Calculate communication normal enable condition
     *
     * Internal method that calculates whether normal communication should
     * be enabled based on diagnostic timing and system state.
     */
    void l_CalcComNormalEnabCond_V(void);

    /** @brief Diagnostic start delay counter */
    UW m_netDiagStartDelayCnt_uw;

    /** @brief Diagnostic start request flag */
    B  m_netDiagStartRequest_b;

    /** @brief Network run request flag */
    B  m_netRunRequest_b;

#if (NETCFG_CAN_PUB_USED)
    /** @brief Bus-off mode instance for Public CAN channel */
    BusoffMode ins_pubBusoffMode;
#endif
#if (NETCFG_CAN_PR1_USED)
    /** @brief Bus-off mode instance for Private CAN1 channel */
    BusoffMode ins_pr1BusoffMode;
#endif
#if (NETCFG_CAN_PR2_USED)
    /** @brief Bus-off mode instance for Private CAN2 channel */
    BusoffMode ins_pr2BusoffMode;
#endif
#if (NETCFG_CAN_PR3_USED)
    /** @brief Bus-off mode instance for Private CAN3 channel */
    BusoffMode ins_pr3BusoffMode;
#endif
};

} // namespace sio_net

//--------------------------------------------------------------------------//
//                                                                          //
// RTE FUNCTION DECLARATION                                                 //
//                                                                          //
//--------------------------------------------------------------------------//

/**
 * @brief Request communication start (C interface)
 *
 * C interface function to request the start of network communication.
 * This function enables network communication through the NetMode instance.
 */
extern "C" void net_mode_req_com_start_v(void);

/**
 * @brief Request communication stop (C interface)
 *
 * C interface function to request the stop of network communication.
 * This function disables network communication through the NetMode instance.
 */
extern "C" void net_mode_req_com_stop_v(void);

/**
 * @brief Request enable network monitor (C interface)
 *
 * C interface function to enable network monitoring and diagnostics.
 * This function enables network diagnostic functionality.
 */
extern "C" void net_mode_req_enable_net_monitor_v(void);

/**
 * @brief Request disable network monitor (C interface)
 *
 * C interface function to disable network monitoring and diagnostics.
 * This function disables network diagnostic functionality.
 */
extern "C" void net_mode_req_disable_net_monitor_v(void);

/**
 * @brief Set diagnostic restart time (C interface)
 *
 * C interface function to set the diagnostic delay counter to restart time.
 * Used after bus-off recovery events.
 */
extern "C" void net_mode_set_diag_restart_time_v(void);

/**
 * @brief Set diagnostic start time (C interface)
 *
 * C interface function to set the diagnostic delay counter to start time.
 * Used during initial system startup.
 */
extern "C" void net_mode_set_diag_start_time_v(void);

/**
 * @brief Set run request flag (C interface)
 *
 * C interface function to set the network run request flag.
 *
 * @param value Boolean value to set (TRUE to enable, FALSE to disable)
 */
extern "C" void net_mode_set_run_request_flag_v(BOOL value);

/**
 * @brief Get run request flag (C interface)
 *
 * C interface function to get the current network run request flag state.
 *
 * @return Current network run request state (TRUE if enabled, FALSE if disabled)
 */
extern "C" BOOL net_mode_get_run_request_flag_B(void);

#endif /* _NETMODE_H_ */
